[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[submodule]
	active = .
[remote "origin"]
	url = https://github.com/hoanmuada/mediasoft_api.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[lfs]
	repositoryformatversion = 0
[branch "feat/article_update_royalty"]
	remote = origin
	merge = refs/heads/feat/article_update_royalty
