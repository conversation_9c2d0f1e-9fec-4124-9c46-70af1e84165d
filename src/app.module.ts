import { HttpStatus, Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppDataSource } from './configs/data-source.conf';
import { Request, Response } from 'express';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';
import appConf from './configs/app.conf';
import constantConf from './configs/constant.conf';
import { GroupActionsModule } from './modules/group-actions/group-actions.module';
import { FilesModule } from './modules/files/files.module';
import * as depthLimit from 'graphql-depth-limit';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AssignIdToBodyInterceptor } from './commons/interceptors/assign-id-to-body.interceptor';
import { DataLoaderModule } from './data-loaders/data-loaders.module';
import { DepartmentsModule } from './modules/departments/departments.module';
import { EmailModule } from './modules/e-mails/email.module';
import { WorkflowsModule } from './modules/workflows/workflows.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { LayoutsModule } from './modules/layouts/layouts.module';
import { TagsModule } from './modules/tags/tags.module';
import { PortletsModule } from './modules/portlets/portlets.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { ArticlesModule } from './modules/articles/articles.module';
import { AdvertisesModule } from './modules/advertises/advertises.module';
import { ConfigsModule } from './modules/configs/configs.module';
import { FoldersModule } from './modules/folders/folders.module';
import { ComplexityPlugin } from './commons/plugins/complexity.plugin';
import { UserDepartmentsModule } from './modules/user-departments/user-departments.module';
import { SqlModule } from './modules/sql/sql.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { DevicesModule } from './modules/devices/devices.module';
import { PressPublicationsModule } from './modules/press-publications/press-publications.module';
import { IssuesModule } from './modules/issues/issues.module';
import { RoyaltyFormulasModule } from './modules/royalty-formulas/royalty-formulas.module';
import { TaskSchedulingModule } from './task-scheduling/task-scheduling.module';
import { LoggerModule } from './loggers/logger.module';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
        }),
        TypeOrmModule.forRoot(AppDataSource.options),
        GraphQLModule.forRoot<ApolloDriverConfig>({
            driver: ApolloDriver,
            autoSchemaFile: join(process.cwd(), 'schema.gql'),
            playground: appConf.NODE_ENV !== 'production', // 🔥 Chỉ bật Playground trong môi trường Dev
            introspection: appConf.NODE_ENV !== 'production', // Chặn introspection khi production
            context: ({ req, res }: { req: Request; res: Response }) => ({ req, res }),
            // Disable CSRF prevention
            csrfPrevention: false,
            // Chỉ áp dụng Apollo Server cho endpoint này
            path: '/graphql',
            formatError: (error) => {
                if (appConf.NODE_ENV !== 'production') console.log('🔥 Errors:', error); // Debug lỗi
                const statusCode = error.extensions?.code ?? HttpStatus.INTERNAL_SERVER_ERROR;
                return {
                    message: error.message,
                    code: statusCode,
                    errors: error.extensions?.errors,
                };
            },
            validationRules: [
                depthLimit(constantConf.graphql.maxDepth), // Limit query depth based on configuration
            ],
            plugins: [
                {
                    async requestDidStart() {
                        return {
                            async willSendResponse({ response, errors }) {
                                if (errors?.length) {
                                    const httpStatus =
                                        // @ts-ignore
                                        (errors[0]?.extensions?.http?.status ?? errors[0]?.extensions?.code) || 500;
                                    if (response.http) {
                                        response.http.status = +httpStatus;
                                    }
                                }
                            },
                        };
                    },
                },
            ],
        }),
        ScheduleModule.forRoot(),
        UsersModule,
        AuthModule,
        GroupActionsModule,
        FilesModule,
        DataLoaderModule,
        DepartmentsModule,
        EmailModule,
        WorkflowsModule,
        CategoriesModule,
        LayoutsModule,
        TagsModule,
        PortletsModule,
        TemplatesModule,
        ArticlesModule,
        AdvertisesModule,
        ConfigsModule,
        FoldersModule,
        ComplexityPlugin,
        UserDepartmentsModule,
        SqlModule,
        NotificationsModule,
        DevicesModule,
        PressPublicationsModule,
        IssuesModule,
        RoyaltyFormulasModule,
        TaskSchedulingModule,
        LoggerModule,
    ],
    providers: [
        {
            provide: APP_INTERCEPTOR,
            useClass: AssignIdToBodyInterceptor,
        },
    ],
})
export class AppModule {}
