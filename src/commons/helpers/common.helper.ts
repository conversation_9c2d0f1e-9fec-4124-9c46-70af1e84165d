import { existsSync, mkdirSync, PathLike } from 'fs';
import { ResponseSuccess } from '../interfaces.common';

export const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const mkdir = (path: PathLike) => {
    if (!existsSync(path)) mkdirSync(path, { recursive: true });
};

export const formatResponse = <T>(data: T, message: string): ResponseSuccess<T> => ({
    data,
    message,
});

/**
 * Trả về ngày tiếp theo rơi vào đúng thứ mong muốn
 * @param startDate Ngày bắt đầu
 * @param targetDay Thứ cần tìm (0 = CN, 1 = T2, ... 6 = T7)
 * @param includeStartDay Nếu true và startDate đã đúng thứ, trả luôn startDate; nếu false thì lấy tuần sau
 */
export const getNextDayOfWeek = (startDate: Date, targetDay: number, includeStartDay = false): Date => {
    const result = new Date(startDate);
    const currentDay = result.getDay();

    let daysToAdd = (targetDay - currentDay + 7) % 7;
    if (daysToAdd === 0 && !includeStartDay) {
        daysToAdd = 7; // nhảy sang tuần sau
    }

    result.setDate(result.getDate() + daysToAdd);
    return result;
};
