import { diskStorage, FileFilterCallback, Options } from 'multer';
import { mkdir } from '../commons/helpers/common.helper';
import { BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { IMAGES_PATH, OTHERS_PATH, UPLOAD_DIR, VIDEOS_PATH, AUDIOS_PATH, DOCUMENTS_PATH } from '../commons/helpers/file.helper';
import appConf from './app.conf';
import slugify from 'slugify';

export const multerConfig = {
    dest: './' + UPLOAD_DIR,
    limitSize: appConf.LIMIT_UPLOAD_SIZE * 1024 * 1024 * 1024, // GB
    maxFiles: appConf.MAX_FILES_PER_UPLOAD,
    allowedFileTypes: appConf.ALLOWED_FILE_TYPES,
    allowedMimeTypes: appConf.ALLOWED_MIME_TYPES,
};

export const multerOptions: Options = {
    storage: diskStorage({
        destination: (_, file, cb) => {
            let subPath: string;
            //Chia subPath theo mimetype
            if (file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|bmp|svg\+xml)$/)) {
                subPath = `/${IMAGES_PATH}`;
            } else if (file.mimetype.startsWith('video/')) {
                subPath = `/${VIDEOS_PATH}`;
            } else if (file.mimetype.startsWith('audio/')) {
                subPath = `/${AUDIOS_PATH}`;
            } else if (file.mimetype.includes('pdf') || 
                       file.mimetype.includes('msword') || 
                       file.mimetype.includes('wordprocessingml') ||
                       file.mimetype.includes('text/plain')) {
                subPath = `/${DOCUMENTS_PATH}`;
            } else {
                subPath = `/${OTHERS_PATH}`;
            }
            subPath += `/${new Date().toISOString().slice(0, 10)}`;
            let uploadPath = multerConfig.dest + subPath;
            mkdir(uploadPath);

            cb(null, uploadPath); // Lưu file vào thư mục chỉ định
        },
        filename: (req, file, cb) => {
            const randomName = Date.now() + '-' + Math.round(Math.random() * 1e9);
            const filename = `${randomName}_${slugify(file.originalname)}`;
            // Gắn vào request để tiện xoá nếu request bị cancel
            if (!req['uploadedFiles']) req['uploadedFiles'] = [];
            req['uploadedFiles'].push({ filename, destination: req['destination'] });
            cb(null, filename);
        },
    }),
    fileFilter: (_: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
        // Check if the MIME type is in the allowed list
        if (!multerConfig.allowedMimeTypes.includes(file.mimetype)) {
            return cb(
                new BadRequestException(
                    `Invalid file type! Only ${multerConfig.allowedFileTypes.join(', ')} are allowed.`
                ) as any,
                false
            );
        }
        cb(null, true);
    },
    limits: {
        fileSize: multerConfig.limitSize,
        files: multerConfig.maxFiles, // Giới hạn số lượng file
    },
};
