import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeIdToWorkflows1754000000000 implements MigrationInterface {
    name = 'AddTypeIdToWorkflows1754000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "workflows" ADD "type_id" smallint DEFAULT 1`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "workflows" DROP COLUMN "type_id"`);
    }
}
