import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVideoIdToArticles1754000004000 implements MigrationInterface {
    name = 'AddVideoIdToArticles1754000004000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" ADD "video_id" integer`);
        await queryRunner.query(`ALTER TABLE "articles" ADD CONSTRAINT "FK_articles_video_id" FOREIGN KEY ("video_id") REFERENCES "files"("id") ON DELETE RESTRICT ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_articles_video_id"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "video_id"`);
    }
}