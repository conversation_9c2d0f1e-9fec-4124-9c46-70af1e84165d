import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticleVideosTable1754000005000 implements MigrationInterface {
    name = 'CreateArticleVideosTable1754000005000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "article_videos" (
                "id" SERIAL NOT NULL,
                "article_id" integer,
                "video_id" integer,
                "file_ids" json,
                "file_comments" json,
                "created_by" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_by" integer,
                "updated_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" integer,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                CONSTRAINT "PK_article_videos" PRIMARY KEY ("id")
            )
        `);

        await queryRunner.query(`
            ALTER TABLE "article_videos" 
            ADD CONSTRAINT "FK_article_videos_article_id" 
            FOREIGN KEY ("article_id") REFERENCES "articles"("id") 
            ON DELETE RESTRICT ON UPDATE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "article_videos" 
            ADD CONSTRAINT "FK_article_videos_video_id" 
            FOREIGN KEY ("video_id") REFERENCES "files"("id") 
            ON DELETE RESTRICT ON UPDATE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_videos" DROP CONSTRAINT "FK_article_videos_video_id"`);
        await queryRunner.query(`ALTER TABLE "article_videos" DROP CONSTRAINT "FK_article_videos_article_id"`);
        await queryRunner.query(`DROP TABLE "article_videos"`);
    }
}
