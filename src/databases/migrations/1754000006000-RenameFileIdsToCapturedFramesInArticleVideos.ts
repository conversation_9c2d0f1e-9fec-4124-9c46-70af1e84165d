import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameFileIdsToCapturedFramesInArticleVideos1754000006000 implements MigrationInterface {
    name = 'RenameFileIdsToCapturedFramesInArticleVideos1754000006000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_videos" RENAME COLUMN "file_ids" TO "captured_frames"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_videos" RENAME COLUMN "captured_frames" TO "file_ids"`);
    }
}