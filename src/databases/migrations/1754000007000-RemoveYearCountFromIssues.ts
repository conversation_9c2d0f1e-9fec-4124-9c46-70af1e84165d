import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveYearCountFromIssues1754000007000 implements MigrationInterface {
    name = 'RemoveYearCountFromIssues1754000007000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issues" DROP COLUMN "year_count"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issues" ADD "year_count" integer`);
    }
}
