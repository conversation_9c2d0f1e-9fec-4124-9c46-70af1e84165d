import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTelevisionWorkflowTables1754000008000 implements MigrationInterface {
    name = 'CreateTelevisionWorkflowTables1754000008000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create television_workflows table
        await queryRunner.query(`
            CREATE TABLE "television_workflows" (
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "desc" character varying,
                "status_id" smallint NOT NULL,
                "created_by" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_by" integer,
                "updated_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" integer,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                CONSTRAINT "PK_television_workflows" PRIMARY KEY ("id")
            )
        `);

        // Create television_workflow_users table
        await queryRunner.query(`
            CREATE TABLE "television_workflow_users" (
                "id" SERIAL NOT NULL,
                "television_workflow_id" integer NOT NULL,
                "user_id" integer NOT NULL,
                "type_id" smallint NOT NULL,
                "created_by" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_by" integer,
                "updated_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" integer,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                CONSTRAINT "PK_television_workflow_users" PRIMARY KEY ("id")
            )
        `);

        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "television_workflow_users" 
            ADD CONSTRAINT "FK_television_workflow_users_television_workflow_id" 
            FOREIGN KEY ("television_workflow_id") REFERENCES "television_workflows"("id") 
            ON DELETE RESTRICT ON UPDATE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "television_workflow_users" 
            ADD CONSTRAINT "FK_television_workflow_users_user_id" 
            FOREIGN KEY ("user_id") REFERENCES "users"("id") 
            ON DELETE RESTRICT ON UPDATE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "television_workflow_users" DROP CONSTRAINT "FK_television_workflow_users_user_id"`);
        await queryRunner.query(`ALTER TABLE "television_workflow_users" DROP CONSTRAINT "FK_television_workflow_users_television_workflow_id"`);
        await queryRunner.query(`DROP TABLE "television_workflow_users"`);
        await queryRunner.query(`DROP TABLE "television_workflows"`);
    }
}