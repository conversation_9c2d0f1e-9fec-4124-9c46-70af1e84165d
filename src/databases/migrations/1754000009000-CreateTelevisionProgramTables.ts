import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTelevisionProgramTables1754000009000 implements MigrationInterface {
    name = 'CreateTelevisionProgramTables1754000009000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create television_programs table
        await queryRunner.query(`
            CREATE TABLE "television_programs" (
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "desc" character varying,
                "status_id" smallint NOT NULL,
                "department_id" integer NOT NULL,
                "created_by" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_by" integer,
                "updated_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" integer,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                CONSTRAINT "PK_television_programs" PRIMARY KEY ("id")
            )
        `);

        // Create television_program_workflows junction table
        await queryRunner.query(`
            CREATE TABLE "television_program_workflows" (
                "television_program_id" integer NOT NULL,
                "television_workflow_id" integer NOT NULL,
                CONSTRAINT "PK_television_program_workflows" PRIMARY KEY ("television_program_id", "television_workflow_id")
            )
        `);

        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "television_programs" 
            ADD CONSTRAINT "FK_television_programs_department_id" 
            FOREIGN KEY ("department_id") REFERENCES "departments"("id") 
            ON DELETE RESTRICT ON UPDATE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "television_program_workflows" 
            ADD CONSTRAINT "FK_television_program_workflows_television_program_id" 
            FOREIGN KEY ("television_program_id") REFERENCES "television_programs"("id") 
            ON DELETE CASCADE ON UPDATE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "television_program_workflows" 
            ADD CONSTRAINT "FK_television_program_workflows_television_workflow_id" 
            FOREIGN KEY ("television_workflow_id") REFERENCES "television_workflows"("id") 
            ON DELETE CASCADE ON UPDATE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "television_program_workflows" DROP CONSTRAINT "FK_television_program_workflows_television_workflow_id"`);
        await queryRunner.query(`ALTER TABLE "television_program_workflows" DROP CONSTRAINT "FK_television_program_workflows_television_program_id"`);
        await queryRunner.query(`ALTER TABLE "television_programs" DROP CONSTRAINT "FK_television_programs_department_id"`);
        await queryRunner.query(`DROP TABLE "television_program_workflows"`);
        await queryRunner.query(`DROP TABLE "television_programs"`);
    }
}