import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticleRoyaltyGroupsTable1754100000000 implements MigrationInterface {
    name = 'CreateArticleRoyaltyGroupsTable1754100000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "article_royalty_groups" (
                "id" SERIAL NOT NULL,
                "article_id" integer,
                "title" character varying(255),
                "workflow_id" smallint,
                "created_by" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_by" integer,
                "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(),
                "deleted_by" integer,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                CONSTRAINT "PK_article_royalty_groups" PRIMARY KEY ("id")
            )
        `);

        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups"
            ADD CONSTRAINT "FK_article_royalty_groups_article_id"
            FOREIGN KEY ("article_id") REFERENCES "articles"("id")
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups"
            ADD CONSTRAINT "FK_article_royalty_groups_workflow_id"
            FOREIGN KEY ("workflow_id") REFERENCES "workflows"("id")
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups"
            DROP CONSTRAINT "FK_article_royalty_groups_article_id"
        `);

        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups"
            DROP CONSTRAINT "FK_article_royalty_groups_workflow_id"
        `);

        await queryRunner.query(`DROP TABLE "article_royalty_groups"`);
    }
}
