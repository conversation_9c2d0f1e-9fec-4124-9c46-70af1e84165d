import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddArticleStatisticToArticleRoyaltyGroups1754200000000 implements MigrationInterface {
    name = 'AddArticleStatisticToArticleRoyaltyGroups1754200000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups"
            ADD "article_statistic" integer DEFAULT 0
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups"
            DROP COLUMN "article_statistic"
        `);
    }
}
