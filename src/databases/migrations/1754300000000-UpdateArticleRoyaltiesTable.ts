import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateArticleRoyaltiesTable1754300000000 implements MigrationInterface {
    name = 'UpdateArticleRoyaltiesTable1754300000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if article_id column exists and drop foreign key constraint if it exists
        const articleIdColumnExists = await queryRunner.hasColumn("article_royalties", "article_id");

        if (articleIdColumnExists) {
            // Get all foreign key constraints for the table
            const foreignKeys = await queryRunner.query(`
                SELECT constraint_name
                FROM information_schema.table_constraints
                WHERE table_name = 'article_royalties'
                AND constraint_type = 'FOREIGN KEY'
                AND constraint_name LIKE '%article_id%'
            `);

            // Drop any foreign key constraints related to article_id
            for (const fk of foreignKeys) {
                try {
                    await queryRunner.query(`
                        ALTER TABLE "article_royalties"
                        DROP CONSTRAINT "${fk.constraint_name}"
                    `);
                } catch (error) {
                    // Continue if constraint doesn't exist
                    console.log(`Constraint ${fk.constraint_name} doesn't exist, skipping...`);
                }
            }

            // Drop the article_id column
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                DROP COLUMN "article_id"
            `);
        }

        // Check if article_statistic column exists and drop it
        const articleStatisticColumnExists = await queryRunner.hasColumn("article_royalties", "article_statistic");
        if (articleStatisticColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                DROP COLUMN "article_statistic"
            `);
        }

        // Check if group_id column already exists
        const groupIdColumnExists = await queryRunner.hasColumn("article_royalties", "group_id");
        if (!groupIdColumnExists) {
            // Add the new group_id column
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                ADD "group_id" integer
            `);
        }

        // Add foreign key constraint for group_id if it doesn't exist
        const groupIdConstraintExists = await queryRunner.query(`
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'article_royalties'
            AND constraint_type = 'FOREIGN KEY'
            AND constraint_name = 'FK_article_royalties_group_id'
        `);

        if (groupIdConstraintExists.length === 0) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                ADD CONSTRAINT "FK_article_royalties_group_id"
                FOREIGN KEY ("group_id") REFERENCES "article_royalty_groups"("id")
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint for group_id if it exists
        try {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                DROP CONSTRAINT "FK_article_royalties_group_id"
            `);
        } catch (error) {
            console.log('FK_article_royalties_group_id constraint does not exist, skipping...');
        }

        // Drop group_id column if it exists
        const groupIdColumnExists = await queryRunner.hasColumn("article_royalties", "group_id");
        if (groupIdColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                DROP COLUMN "group_id"
            `);
        }

        // Add back the dropped columns if they don't exist
        const articleIdColumnExists = await queryRunner.hasColumn("article_royalties", "article_id");
        if (!articleIdColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                ADD "article_id" integer
            `);
        }

        const articleStatisticColumnExists = await queryRunner.hasColumn("article_royalties", "article_statistic");
        if (!articleStatisticColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                ADD "article_statistic" integer DEFAULT 0
            `);
        }

        // Add back foreign key constraint for article_id if it doesn't exist
        const articleIdConstraintExists = await queryRunner.query(`
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'article_royalties'
            AND constraint_type = 'FOREIGN KEY'
            AND constraint_name = 'FK_article_royalties_article_id'
        `);

        if (articleIdConstraintExists.length === 0) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties"
                ADD CONSTRAINT "FK_article_royalties_article_id"
                FOREIGN KEY ("article_id") REFERENCES "articles"("id")
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
        }
    }
}
