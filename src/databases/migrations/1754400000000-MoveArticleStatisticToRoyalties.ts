import { MigrationInterface, QueryRunner } from 'typeorm';

export class MoveArticleStatisticToRoyalties1754400000000 implements MigrationInterface {
    name = 'MoveArticleStatisticToRoyalties1754400000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add article_statistic to article_royalties
        await queryRunner.query(`
            ALTER TABLE "article_royalties" 
            ADD "article_statistic" integer DEFAULT 0
        `);

        // Drop article_statistic from article_royalty_groups
        const articleStatisticColumnExists = await queryRunner.hasColumn('article_royalty_groups', 'article_statistic');
        if (articleStatisticColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalty_groups" 
                DROP COLUMN "article_statistic"
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add back article_statistic to article_royalty_groups
        const groupStatisticColumnExists = await queryRunner.hasColumn('article_royalty_groups', 'article_statistic');
        if (!groupStatisticColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalty_groups" 
                ADD "article_statistic" integer DEFAULT 0
            `);
        }

        // Drop article_statistic from article_royalties
        const royaltyStatisticColumnExists = await queryRunner.hasColumn('article_royalties', 'article_statistic');
        if (royaltyStatisticColumnExists) {
            await queryRunner.query(`
                ALTER TABLE "article_royalties" 
                DROP COLUMN "article_statistic"
            `);
        }
    }
}
