import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShowMenuAndAvatarToCategories1754500000000 implements MigrationInterface {
    name = 'AddShowMenuAndAvatarToCategories1754500000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add show_menu column
        await queryRunner.query(`
            ALTER TABLE "categories" 
            ADD "show_menu" boolean DEFAULT false
        `);

        // Add avatar_id column
        await queryRunner.query(`
            ALTER TABLE "categories" 
            ADD "avatar_id" integer
        `);

        // Add foreign key constraint for avatar_id
        await queryRunner.query(`
            ALTER TABLE "categories"
            ADD CONSTRAINT "FK_categories_avatar_id"
            FOREIGN KEY ("avatar_id") REFERENCES "files"("id")
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "categories"
            DROP CONSTRAINT "FK_categories_avatar_id"
        `);

        // Drop avatar_id column
        await queryRunner.query(`
            ALTER TABLE "categories" 
            DROP COLUMN "avatar_id"
        `);

        // Drop show_menu column
        await queryRunner.query(`
            ALTER TABLE "categories" 
            DROP COLUMN "show_menu"
        `);
    }
}
