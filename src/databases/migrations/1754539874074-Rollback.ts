import { MigrationInterface, QueryRunner } from 'typeorm';

export class Rollback1754539874074 implements MigrationInterface {
    name = 'Rollback1754539874074';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_categories_avatar_id"`);
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_article_royalties_group_id"`);
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_article_royalty_groups_article_id"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_article_royalty_groups_workflow_id"`
        );
        await queryRunner.query(
            `CREATE TABLE "article_logs" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "content" json NOT NULL, CONSTRAINT " PK_700df37970df19c025c65e9334f" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "categories" ALTER COLUMN "show_menu" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ALTER COLUMN "created_by" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" DROP COLUMN "title"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ADD "title" character varying`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" DROP COLUMN "workflow_id"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ADD "workflow_id" integer`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ALTER COLUMN "article_type_id" DROP DEFAULT`);
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_3767b7366707b8e251b82044999" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_54ee1a563dac190e6fd5272ccc1" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_03c2ec4e48f1312ae0fa0b12420" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_61508974bbf5f091fdb74043782" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_361adc823497a4846eaa7de24fb" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_69b66c0b83f3bbe1d59e923ac77" FOREIGN KEY ("group_id") REFERENCES "article_royalty_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_dc1dad36a2d20d17daebfa8a3a8" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_6fef2e1515a9c4ca855e03a5320" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_d91a092a0f761f829d6173e3d40" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_c3fa23332dcd260fda5bd78062b" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_f0b54cb632fc85be759e162b4dc" FOREIGN KEY ("workflow_id") REFERENCES "workflows"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_f0b54cb632fc85be759e162b4dc"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_c3fa23332dcd260fda5bd78062b"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_d91a092a0f761f829d6173e3d40"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_6fef2e1515a9c4ca855e03a5320"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" DROP CONSTRAINT "FK_dc1dad36a2d20d17daebfa8a3a8"`
        );
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_69b66c0b83f3bbe1d59e923ac77"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_361adc823497a4846eaa7de24fb"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_61508974bbf5f091fdb74043782"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_03c2ec4e48f1312ae0fa0b12420"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_54ee1a563dac190e6fd5272ccc1"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_3767b7366707b8e251b82044999"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ALTER COLUMN "article_type_id" SET DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" DROP COLUMN "workflow_id"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ADD "workflow_id" smallint`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" DROP COLUMN "title"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ADD "title" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "article_royalty_groups" ALTER COLUMN "created_by" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "categories" ALTER COLUMN "show_menu" DROP NOT NULL`);
        await queryRunner.query(`DROP TABLE "article_logs"`);
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_article_royalty_groups_workflow_id" FOREIGN KEY ("workflow_id") REFERENCES "workflows"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_groups" ADD CONSTRAINT "FK_article_royalty_groups_article_id" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_article_royalties_group_id" FOREIGN KEY ("group_id") REFERENCES "article_royalty_groups"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_categories_avatar_id" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
    }
}
