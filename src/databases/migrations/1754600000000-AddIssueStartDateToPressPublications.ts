import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIssueStartDateToPressPublications1754600000000 implements MigrationInterface {
    name = 'AddIssueStartDateToPressPublications1754600000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add issue_start_date column
        await queryRunner.query(`
            ALTER TABLE "press_publications"
            ADD "issue_start_date" date
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop issue_start_date column
        await queryRunner.query(`
            ALTER TABLE "press_publications" 
            DROP COLUMN "issue_start_date"
        `);
    }
}
