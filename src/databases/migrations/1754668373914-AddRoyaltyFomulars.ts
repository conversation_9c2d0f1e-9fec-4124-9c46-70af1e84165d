import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRoyaltyFormulas1754668373914 implements MigrationInterface {
    name = 'AddRoyaltyFormulas1754668373914';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "royalty_formulas" (
                "id" SERIAL NOT NULL,
                "order" smallint NOT NULL DEFAULT 0,
                "article_type_id" smallint NOT NULL,
                "formula" text NOT NULL,
                "status_id" smallint NOT NULL DEFAULT 1,
                "department_id" integer NOT NULL,
                "created_by" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_by" integer NULL,
                "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(),
                "deleted_by" integer NULL,
                "deleted_at" TIMESTAMP WITH TIME ZONE NULL,
                CONSTRAINT "PK_royalty_formulas_id" PRIMARY KEY ("id"),
                CONSTRAINT "FK_royalty_formulas_department_id"
                FOREIGN KEY ("department_id") REFERENCES "departments"("id")
                ON DELETE RESTRICT ON UPDATE NO ACTION,
                CONSTRAINT "FK_royalty_formulas_created_by"
                FOREIGN KEY ("created_by") REFERENCES "users"("id")
                ON DELETE RESTRICT ON UPDATE NO ACTION,
                CONSTRAINT "FK_royalty_formulas_updated_by"
                FOREIGN KEY ("updated_by") REFERENCES "users"("id")
                ON DELETE RESTRICT ON UPDATE NO ACTION,
                CONSTRAINT "FK_royalty_formulas_deleted_by"
                FOREIGN KEY ("deleted_by") REFERENCES "users"("id")
                ON DELETE RESTRICT ON UPDATE NO ACTION,
                CONSTRAINT "UK_royalty_formulas_article_type_department" UNIQUE ("article_type_id", "department_id")
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "royalty_formulas"
            DROP CONSTRAINT "FK_royalty_formulas_department_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "royalty_formulas"
            DROP CONSTRAINT "FK_royalty_formulas_created_by"
        `);
        await queryRunner.query(`
            ALTER TABLE "royalty_formulas"
            DROP CONSTRAINT "FK_royalty_formulas_updated_by"
        `);
        await queryRunner.query(`
            ALTER TABLE "royalty_formulas"
            DROP CONSTRAINT "FK_royalty_formulas_deleted_by"
        `);

        await queryRunner.query(`DROP TABLE "royalty_formulas"`);
    }
}
