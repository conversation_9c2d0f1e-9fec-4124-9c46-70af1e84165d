import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsFixedPublishDateToArticles1754700000000 implements MigrationInterface {
    name = 'AddIsFixedPublishDateToArticles1754700000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add is_fixed_publish_date column
        await queryRunner.query(`
            ALTER TABLE "articles" 
            ADD "is_fixed_publish_date" boolean DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop is_fixed_publish_date column
        await queryRunner.query(`
            ALTER TABLE "articles" 
            DROP COLUMN "is_fixed_publish_date"
        `);
    }
}
