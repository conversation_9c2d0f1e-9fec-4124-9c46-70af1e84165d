import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPublishDateToArticleRoyaltyGroups1754800000000 implements MigrationInterface {
    name = 'AddPublishDateToArticleRoyaltyGroups1754800000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add publish_date column
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups" 
            ADD "publish_date" timestamptz
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop publish_date column
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups" 
            DROP COLUMN "publish_date"
        `);
    }
}
