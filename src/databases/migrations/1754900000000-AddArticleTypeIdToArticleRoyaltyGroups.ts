import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddArticleTypeIdToArticleRoyaltyGroups1754900000000 implements MigrationInterface {
    name = 'AddArticleTypeIdToArticleRoyaltyGroups1754900000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add article_type_id column
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups" 
            ADD "article_type_id" smallint NOT NULL DEFAULT 1
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop article_type_id column
        await queryRunner.query(`
            ALTER TABLE "article_royalty_groups" 
            DROP COLUMN "article_type_id"
        `);
    }
}
