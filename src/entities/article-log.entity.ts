import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { WorkflowEntity } from './workflow.entity';

@ObjectType('article_logs')
@Entity('article_logs')
export class ArticleLogEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column({ type: 'json' })
    @Field()
    content: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    old_workflow_id: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    new_workflow_id: number;

    @ManyToOne(() => ArticleEntity, (article) => article.articleLogs)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => WorkflowEntity)
    @JoinColumn({ name: 'old_workflow_id' })
    @Field(() => WorkflowEntity)
    oldWorkflow: WorkflowEntity;

    @ManyToOne(() => WorkflowEntity)
    @JoinColumn({ name: 'new_workflow_id' })
    @Field(() => WorkflowEntity)
    newWorkflow: WorkflowEntity;
}
