import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, GraphQLISODateTime, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { WorkflowEntity } from './workflow.entity';
import { ArticleRoyaltyEntity } from './article-royalty.entity';
import { ArticleTypes } from './workflow-permission-article-type.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('article_royalty_groups')
@Entity('article_royalty_groups')
@SearchFields(['title'])
export class ArticleRoyaltyGroupEntity extends BaseEntity {
    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    article_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    title?: string;

    @Column({ type: 'smallint', nullable: true })
    @Field(() => Int, { nullable: true })
    workflow_id?: number;

    @Column({ type: 'timestamptz', nullable: true })
    @Field(() => GraphQLISODateTime, { nullable: true })
    publish_date?: Date;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    article_type_id: ArticleTypes;

    /* Relationships */
    @ManyToOne(() => ArticleEntity, { nullable: true })
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity, { nullable: true })
    article?: ArticleEntity;

    @ManyToOne(() => WorkflowEntity, { nullable: true })
    @JoinColumn({ name: 'workflow_id' })
    @Field(() => WorkflowEntity, { nullable: true })
    workflow?: WorkflowEntity;

    @OneToMany(() => ArticleRoyaltyEntity, (ar) => ar.group)
    @Field(() => [ArticleRoyaltyEntity], { nullable: true })
    articleRoyalties?: ArticleRoyaltyEntity[];
}
