import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleRoyaltyGroupEntity } from './article-royalty-group.entity';
import { ArticleRoyaltyTypeEntity } from './article-royalty-type.entity';
import { ArticleRoyaltyUserEntity } from './article-royalty-user.entity';

export enum RoyaltyType {
    CONTENT = 1,
    MEDIA = 2,
    SUPPORT = 3,
}

registerEnumType(RoyaltyType, {
    name: 'RoyaltyType',
});

@ObjectType('article_royalties')
@Entity('article_royalties')
export class ArticleRoyaltyEntity extends BaseEntity {
    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    group_id?: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    type_id: RoyaltyType;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    royalty_type_id?: number;

    @Column({ type: 'int' })
    @Field(() => Int)
    suggest_royalty: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    title?: string;

    @Column({ type: 'int', nullable: true, default: 0 })
    @Field(() => Int, { nullable: true })
    article_statistic?: number;

    /* Relationships */
    @ManyToOne(() => ArticleRoyaltyGroupEntity, { nullable: true })
    @JoinColumn({ name: 'group_id' })
    @Field(() => ArticleRoyaltyGroupEntity, { nullable: true })
    group?: ArticleRoyaltyGroupEntity;

    @ManyToOne(() => ArticleRoyaltyTypeEntity, { nullable: true })
    @JoinColumn({ name: 'royalty_type_id' })
    @Field(() => ArticleRoyaltyTypeEntity, { nullable: true })
    royaltyType?: ArticleRoyaltyTypeEntity;

    @OneToMany(() => ArticleRoyaltyUserEntity, (aru) => aru.articleRoyalty)
    @Field(() => [ArticleRoyaltyUserEntity], { nullable: true })
    articleRoyaltyUsers?: ArticleRoyaltyUserEntity[];
}
