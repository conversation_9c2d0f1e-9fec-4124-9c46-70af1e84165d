import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { FileEntity } from './file.entity';

@ObjectType('article_videos')
@Entity('article_videos')
export class ArticleVideoEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field(() => Int)
    video_id: number;

    @Column('json', { nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    captured_frames?: any;

    @Column('json', { nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    file_comments?: any;

    /* Relationships */
    @ManyToOne(() => ArticleEntity, (article) => article.articleVideos, { nullable: true })
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity, { nullable: true })
    article?: ArticleEntity;

    @ManyToOne(() => FileEntity, { nullable: true })
    @JoinColumn({ name: 'video_id' })
    @Field(() => FileEntity, { nullable: true })
    video?: FileEntity;
}
