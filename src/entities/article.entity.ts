import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, GraphQLISODateTime, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleTypes } from './workflow-permission-article-type.entity';
import { FileEntity } from './file.entity';
import { WorkflowEntity } from './workflow.entity';
import { PseudonymsEntity } from './pseudonyms.entity';
import { LayoutEntity } from './layout.entity';
import { DepartmentEntity } from './department.entity';
import { UserEntity } from './user.entity';
import { ArticleArticleKindEntity } from './article-article-kind.entity';
import { ArticleCategoryEntity } from './article-category.entity';
import { ArticleTagEntity } from './article-tag.entity';
import { ArticleFileEntity } from './article-file.entity';
import { ArticleNoteEntity } from './article-note.entity';
import { ArticleCommentEntity } from './article-comment.entity';
import { ArticleLogEntity } from './article-log.entity';
import { RelatedArticleEntity } from './related-article.entity';
import { ArticleIssuePageEntity } from './article-issue-page.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { PressPublicationEntity } from './press-publication.entity';
import { IssueEntity } from './issue.entity';
import { ArticleRoyaltyGroupEntity } from './article-royalty-group.entity';

@ObjectType('articles')
@Entity('articles')
@SearchFields(['title', 'sub_title', 'brief_title'])
export class ArticleEntity extends BaseEntity {
    @Column()
    @Field()
    title: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    sub_title?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    brief_title?: string;

    @Column()
    @Field()
    slug: string;

    @Column({ type: 'text', nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'text' })
    @Field()
    content: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    avatar1_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    avatar2_id?: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    workflow_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    article_type_id: ArticleTypes;

    @Column()
    @Field(() => Int)
    pseudonym_id: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ nullable: true, type: 'timestamptz' })
    @Field(() => GraphQLISODateTime, { nullable: true })
    publish_date?: Date;

    @Column({ type: 'boolean', nullable: true, default: false })
    @Field(() => Boolean, { nullable: true })
    is_fixed_publish_date?: boolean;

    @Column({ nullable: true })
    @Field({ nullable: true })
    source?: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    file_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    root_article_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    lock_user_id?: number | null;

    @Column({ nullable: true, type: 'timestamptz' })
    @Field(() => GraphQLISODateTime, { nullable: true })
    lock_at?: Date | null;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    is_sync: boolean;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    web_layout_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    mobile_layout_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    press_publication_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    issue_id?: number;

    @Column({ nullable: true, type: 'smallint' })
    @Field(() => Int, { nullable: true })
    typesetting_status_id?: TypesettingStatus;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    is_unclassified: boolean;

    /* Relationships */
    @ManyToOne(() => FileEntity)
    @JoinColumn({ name: 'avatar1_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar1?: FileEntity;

    @ManyToOne(() => FileEntity)
    @JoinColumn({ name: 'avatar2_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar2?: FileEntity;

    @ManyToOne(() => WorkflowEntity)
    @JoinColumn({ name: 'workflow_id' })
    @Field(() => WorkflowEntity)
    workflow: WorkflowEntity;

    @ManyToOne(() => PseudonymsEntity)
    @JoinColumn({ name: 'pseudonym_id' })
    @Field(() => PseudonymsEntity)
    pseudonym: PseudonymsEntity;

    @ManyToOne(() => DepartmentEntity)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;

    @ManyToOne(() => ArticleEntity, (article) => article.childrenArticles)
    @JoinColumn({ name: 'root_article_id' })
    @Field(() => ArticleEntity, { nullable: true })
    rootArticle?: ArticleEntity;

    @OneToMany(() => ArticleEntity, (article) => article.rootArticle)
    @Field(() => [ArticleEntity], { nullable: true })
    childrenArticles?: ArticleEntity[];

    @ManyToOne(() => UserEntity)
    @JoinColumn({ name: 'lock_user_id' })
    @Field(() => UserEntity, { nullable: true })
    lockUser?: UserEntity;

    @OneToMany(() => ArticleArticleKindEntity, (aak) => aak.article)
    articleKinds?: ArticleArticleKindEntity[];

    @OneToMany(() => ArticleCategoryEntity, (ac) => ac.article)
    @Field(() => [ArticleCategoryEntity], { nullable: true })
    articleCategories?: ArticleCategoryEntity[];

    @OneToMany(() => ArticleTagEntity, (at) => at.article)
    @Field(() => [ArticleTagEntity], { nullable: true })
    articleTags?: ArticleTagEntity[];

    @OneToMany(() => ArticleFileEntity, (af) => af.article)
    @Field(() => [ArticleFileEntity], { nullable: true })
    articleFiles?: ArticleFileEntity[];

    @OneToMany(() => ArticleNoteEntity, (an) => an.article)
    @Field(() => [ArticleNoteEntity], { nullable: true })
    articleNotes?: ArticleNoteEntity[];

    @OneToMany(() => ArticleCommentEntity, (ac) => ac.article)
    @Field(() => [ArticleCommentEntity], { nullable: true })
    articleComments?: ArticleCommentEntity[];

    @OneToMany(() => ArticleLogEntity, (al) => al.article)
    @Field(() => [ArticleLogEntity], { nullable: true })
    articleLogs?: ArticleLogEntity[];

    @OneToMany(() => RelatedArticleEntity, (ra) => ra.article)
    @Field(() => [RelatedArticleEntity], { nullable: true })
    relatedArticles?: RelatedArticleEntity[];

    @OneToMany(() => RelatedArticleEntity, (ra) => ra.relatedArticle)
    @Field(() => [RelatedArticleEntity], { nullable: true })
    relatedToArticles?: RelatedArticleEntity[];

    @ManyToOne(() => LayoutEntity, (layout) => layout.webArticles)
    @JoinColumn({ name: 'web_layout_id' })
    @Field(() => LayoutEntity, { nullable: true })
    articleWebLayout?: LayoutEntity;

    @ManyToOne(() => LayoutEntity, (layout) => layout.mobileArticles)
    @JoinColumn({ name: 'mobile_layout_id' })
    @Field(() => LayoutEntity, { nullable: true })
    articleMobileLayout?: LayoutEntity;

    @ManyToOne(() => PressPublicationEntity, (pp) => pp.articles)
    @JoinColumn({ name: 'press_publication_id' })
    @Field(() => PressPublicationEntity, { nullable: true })
    pressPublication?: PressPublicationEntity;

    @ManyToOne(() => IssueEntity, (issue) => issue.articles)
    @JoinColumn({ name: 'issue_id' })
    @Field(() => IssueEntity, { nullable: true })
    issue?: IssueEntity;

    @OneToMany(() => ArticleIssuePageEntity, (articleIssuePage) => articleIssuePage.article)
    @Field(() => [ArticleIssuePageEntity], { nullable: true })
    articleIssuePages?: ArticleIssuePageEntity[];



    @OneToOne(() => ArticleRoyaltyGroupEntity, (arg) => arg.article)
    @Field(() => ArticleRoyaltyGroupEntity, { nullable: true })
    articleRoyaltyGroup?: ArticleRoyaltyGroupEntity;
}

export enum TypesettingStatus {
    WAIT = 1,
    DOING = 2,
    DONE = 3,
}

registerEnumType(TypesettingStatus, {
    name: 'TypesettingStatus',
});
