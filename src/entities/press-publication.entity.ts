import { <PERSON>umn, <PERSON>tity, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { FileEntity } from './file.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('press_publications')
@Entity('press_publications')
@SearchFields(['name', 'issue_title'])
export class PressPublicationEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_order: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    avatar_id?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    issue_title?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    issue_status_id: ItemStatus;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    issue_type_id: IssueType;

    @Column({ type: 'json', default: [1, 2] })
    @Field(() => [Int])
    issue_days: number[];

    @Column({ type: 'smallint' })
    @Field(() => Int)
    issue_offset: number;

    @Column({ type: 'date', nullable: true })
    @Field(() => String, { nullable: true })
    issue_start_date?: Date;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ type: 'smallint', default: 0 })
    @Field(() => Int)
    page_count: number;

    @Column({ type: 'smallint', default: 0 })
    @Field(() => Int)
    issue_pre_created: number;

    @Column({ type: 'smallint', nullable: true })
    @Field(() => Int, { nullable: true })
    page_size_id?: PageSize;

    @ManyToOne(() => DepartmentEntity, (d) => d.pressPublications)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'avatar_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar?: FileEntity;

    @OneToMany(() => IssueEntity, (issue) => issue.pressPublication)
    @Field(() => [IssueEntity], { nullable: true })
    issues?: IssueEntity[];

    @OneToMany(() => IssuePageEntity, (issuePage) => issuePage.pressPublication)
    @Field(() => [IssuePageEntity], { nullable: true })
    issuePages?: IssuePageEntity[];

    @OneToMany(() => ArticleEntity, (article) => article.pressPublication)
    @Field(() => [ArticleEntity], { nullable: true })
    articles?: ArticleEntity[];
}

export enum IssueType {
    // DAILY = 1,
    WEEKLY = 2,
    MONTHLY = 3,
    YEARLY = 4,
}

registerEnumType(IssueType, {
    name: 'IssueType',
});

export enum PageSize {
    A3 = 1,
    A4 = 2,
}

registerEnumType(PageSize, {
    name: 'PageSize',
});

// Import IssueEntity to avoid circular dependency
import { IssueEntity } from './issue.entity';
import { IssuePageEntity } from './issue-page.entity';
import { ArticleEntity } from './article.entity';
