import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';

@ObjectType('royalty_formulas')
@Entity('royalty_formulas')
export class RoyaltyFormulaEntity extends BaseEntity {
    @Column({ type: 'smallint', default: 0 })
    @Field(() => Int)
    order: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    article_type_id: number;

    @Column({ type: 'text' })
    @Field()
    formula: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field(() => Int)
    department_id: number;

    /* Relationships */
    @ManyToOne(() => DepartmentEntity)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity, { nullable: true })
    department?: DepartmentEntity;
}
