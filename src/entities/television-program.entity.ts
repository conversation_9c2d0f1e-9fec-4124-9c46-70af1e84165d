import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, JoinT<PERSON>, ManyToMany, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { TelevisionWorkflowEntity } from './television-workflow.entity';
import { DepartmentEntity } from './department.entity';

@ObjectType('television_programs')
@Entity('television_programs')
@SearchFields(['name', 'desc'])
export class TelevisionProgramEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.workflows)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @ManyToMany(() => TelevisionWorkflowEntity)
    @JoinTable({
        name: 'television_program_workflows',
        joinColumn: { name: 'television_program_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'television_workflow_id', referencedColumnName: 'id' }
    })
    televisionWorkflows?: TelevisionWorkflowEntity[];
}