import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { TelevisionWorkflowEntity } from './television-workflow.entity';

@ObjectType('television_workflow_users')
@Entity('television_workflow_users')
export class TelevisionWorkflowUserEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    television_workflow_id: number;

    @Column()
    @Field(() => Int)
    user_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    type_id: RoyaltyType;

    /* Relationships */
    @ManyToOne(() => TelevisionWorkflowEntity, (workflow) => workflow.televisionWorkflowUsers)
    @JoinColumn({ name: 'television_workflow_id' })
    televisionWorkflow: TelevisionWorkflowEntity;

    @ManyToOne('UserEntity')
    @JoinColumn({ name: 'user_id' })
    user: any;
}