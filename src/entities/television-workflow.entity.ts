import { Column, Entity, OneToMany } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { TelevisionWorkflowUserEntity } from './television-workflow-user.entity';

@ObjectType('television_workflows')
@Entity('television_workflows')
@SearchFields(['name', 'desc'])
export class TelevisionWorkflowEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    /* Relationships */
    @OneToMany(() => TelevisionWorkflowUserEntity, (workflowUser) => workflowUser.televisionWorkflow)
    televisionWorkflowUsers?: TelevisionWorkflowUserEntity[];
}