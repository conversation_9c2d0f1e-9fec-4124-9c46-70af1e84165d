import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { TelevisionWorkflowUserEntity } from './television-workflow-user.entity';
import { DepartmentEntity } from './department.entity';

@ObjectType('television_workflows')
@Entity('television_workflows')
@SearchFields(['name', 'desc'])
export class TelevisionWorkflowEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field(() => Int)
    department_id: number;

    /* Relationships */
    @ManyToOne(() => DepartmentEntity)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => TelevisionWorkflowUserEntity, (workflowUser) => workflowUser.televisionWorkflow)
    televisionWorkflowUsers?: TelevisionWorkflowUserEntity[];
}