import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

export enum WorkflowType {
    PRIVATE = 1,
    EDITING = 2,
    PUBLISHED = 3,
    TIMING_PUBLISHING = 4,
}

export enum ItemWorkflowType {
    ARTICLE = 1,
    ROYALTY = 2,
}

@ObjectType('workflows')
@Entity('workflows')
@SearchFields(['name'])
export class WorkflowEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field()
    display_order: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    workflow_type_id: WorkflowType;

    @Column({ type: 'smallint', nullable: true, default: 1 })
    @Field(() => Int, { nullable: true })
    type_id?: ItemWorkflowType;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.workflows)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}
