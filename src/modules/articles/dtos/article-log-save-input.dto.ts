import { Field, InputType, Int } from '@nestjs/graphql';
import { IsJSON, IsNotEmpty, IsOptional } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ArticleEntity } from '../../../entities/article.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleLogSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleEntity)
    article_id: number;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsJSON()
    content: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(WorkflowEntity)
    old_workflow_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(WorkflowEntity)
    new_workflow_id?: number;
}
