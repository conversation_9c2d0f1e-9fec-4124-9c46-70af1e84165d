import { Field, InputType, Int } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import {
    IsArray,
    IsEnum,
    IsInt,
    IsNotEmpty,
    IsOptional,
    IsString,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    Validate<PERSON>ested,
} from 'class-validator';
import GraphQLJSON from 'graphql-type-json';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleEntity } from '../../../entities/article.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { RoyaltyType } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';

@InputType()
export class ArticleRoyaltyUserNestedInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserEntity)
    user_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    @Max(100)
    percent: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    param_config?: Array<{ value: string; label: string }>;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    final_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    suggest_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    file_id?: number;
}

@InputType()
export class ArticleRoyaltyNestedInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(RoyaltyType)
    type_id: RoyaltyType;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleRoyaltyTypeEntity)
    royalty_type_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    suggest_royalty: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    title?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    article_statistic?: number;

    @Field(() => [ArticleRoyaltyUserNestedInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyUserNestedInput)
    article_royalty_users?: ArticleRoyaltyUserNestedInput[];
}

@InputType()
@AutoSanitize()
export class ArticleRoyaltyGroupSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @IdExists(ArticleEntity)
    article_id?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    title?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @IdExists(WorkflowEntity)
    workflow_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;

    @Field(() => [ArticleRoyaltyNestedInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyNestedInput)
    article_royalties?: ArticleRoyaltyNestedInput[];
}
