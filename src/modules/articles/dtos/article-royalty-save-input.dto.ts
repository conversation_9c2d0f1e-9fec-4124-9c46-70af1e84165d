import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, Min } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleRoyaltyGroupEntity } from '../../../entities/article-royalty-group.entity';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { RoyaltyType } from '../../../entities/article-royalty.entity';

@InputType()
@AutoSanitize()
export class ArticleRoyaltySaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleRoyaltyGroupEntity)
    group_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(RoyaltyType)
    type_id: RoyaltyType;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleRoyaltyTypeEntity)
    royalty_type_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    suggest_royalty: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    title?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    article_statistic?: number;
}
