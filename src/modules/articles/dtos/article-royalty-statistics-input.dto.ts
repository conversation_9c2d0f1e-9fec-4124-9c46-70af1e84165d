import { Field, GraphQLISODateTime, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsDate, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleRoyaltyStatisticsInputDto {
    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    user_ids?: number[];

    @Field(() => GraphQLISODateTime)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsDate()
    @Type(() => Date)
    start_date: Date;

    @Field(() => GraphQLISODateTime)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsDate()
    @Type(() => Date)
    end_date: Date;
}
