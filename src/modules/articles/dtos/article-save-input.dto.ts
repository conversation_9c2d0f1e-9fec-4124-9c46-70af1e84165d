import { Field, GraphQLISODateTime, InputType, Int, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsEnum,
    IsInt,
    IsNotEmpty,
    IsOptional,
    IsPositive,
    IsString,
    ValidateNested,
    Max,
    Min,
} from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ArticleEntity, TypesettingStatus } from '../../../entities/article.entity';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';
import { ArticleKind } from '../../../entities/article-article-kind.entity';
import { RoyaltyType } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { UserEntity } from '../../../entities/user.entity';
import { FileEntity } from '../../../entities/file.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { LayoutEntity } from '../../../entities/layout.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { TemplateEntity } from '../../../entities/template.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { Type } from 'class-transformer';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { IssueEntity } from '../../../entities/issue.entity';
import { IssuePageEntity } from '../../../entities/issue-page.entity';

@InputType()
export class ArticleRoyaltyUserCreateInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserEntity)
    user_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Max(100)
    percent: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsArray()
    param_config?: Array<{ value: string; label: string }>;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    final_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    suggest_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    file_id?: number;
}

@InputType()
export class ArticleRoyaltyCreateInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(RoyaltyType)
    type_id: RoyaltyType;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleRoyaltyTypeEntity)
    royalty_type_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    suggest_royalty: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    title?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    article_statistic?: number;

    @Field(() => [ArticleRoyaltyUserCreateInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyUserCreateInput)
    article_royalty_users?: ArticleRoyaltyUserCreateInput[];
}

@InputType()
export class ArticleRoyaltyGroupCreateInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    title: string;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    workflow_id?: number;

    @Field(() => [ArticleRoyaltyCreateInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyCreateInput)
    article_royalties?: ArticleRoyaltyCreateInput[];
}

@InputType()
@AutoSanitize()
export class ArticleSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    title: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    sub_title?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    brief_title?: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    slug: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar1_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar2_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(WorkflowEntity)
    workflow_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(PseudonymsEntity)
    pseudonym_id?: number;

    @IsString()
    @Field(() => String, { nullable: true })
    @IsOptional()
    pseudonym_name?: string;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    publish_date?: Date;

    @Field(() => Boolean, { nullable: true, defaultValue: false })
    @IsOptional()
    @IsBoolean()
    is_fixed_publish_date?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    source?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    file_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    video_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(TemplateEntity)
    template_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleEntity)
    root_article_id?: number;

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsEnum(ArticleKind, { each: true })
    article_kind_ids?: ArticleKind[];

    @Field(() => [String], { nullable: true })
    @IsOptional()
    article_tags?: string[];

    @Field(() => [String], { nullable: true })
    @IsOptional()
    new_article_notes?: string[];

    @Field(() => [ArticleCategoryInput], { nullable: true })
    @IsOptional()
    article_categories?: ArticleCategoryInput[];

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    related_article_ids?: number[];

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    article_type_copy_ids?: number[];

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    is_sync?: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(LayoutEntity)
    web_layout_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(LayoutEntity)
    mobile_layout_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(PressPublicationEntity)
    press_publication_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(IssueEntity)
    issue_id?: number;

    @Field(() => [ArticleIssuePageInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleIssuePageInput)
    article_issue_pages?: ArticleIssuePageInput[];

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(TypesettingStatus)
    typesetting_status_id?: TypesettingStatus;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    is_unclassified?: boolean;

    @Field(() => [ArticleRoyaltyCreateInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyCreateInput)
    article_royalties?: ArticleRoyaltyCreateInput[];

    @Field(() => ArticleRoyaltyGroupCreateInput, { nullable: true })
    @IsOptional()
    @ValidateNested()
    @Type(() => ArticleRoyaltyGroupCreateInput)
    article_royalty_group?: ArticleRoyaltyGroupCreateInput;
}

@InputType()
@ObjectType()
export class ArticleCategoryInput {
    @Field(() => Int)
    category_id: number;

    @Field(() => Int)
    display_order: number;

    @Field(() => Boolean)
    is_major: boolean;
}

@InputType()
@ObjectType()
class ArticleIssuePageInput {
    @Field(() => Int)
    @IsOptional()
    @IdExists(IssuePageEntity)
    issue_page_id: number;

    @Field(() => Int)
    @IsInt()
    @IsPositive()
    display_order: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    position?: any;
}
