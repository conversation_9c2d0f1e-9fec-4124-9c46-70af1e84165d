import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class ArticleVideoCommentUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    article_video_id: number;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    image_ids?: string[];

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;
}