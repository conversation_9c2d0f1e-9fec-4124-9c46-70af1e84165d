import { Field, InputType, Int } from '@nestjs/graphql';
import { IsNotEmpty } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';

@InputType()
export class ArticleVideoDeleteFrameInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    article_video_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    image_id: number;
}
