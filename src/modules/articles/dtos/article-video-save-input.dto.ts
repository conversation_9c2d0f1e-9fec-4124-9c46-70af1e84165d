import { Field, InputType, Int } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { IsArray, IsInt, IsOptional } from 'class-validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleEntity } from '../../../entities/article.entity';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
@AutoSanitize()
export class ArticleVideoSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsInt()
    @IdExists(ArticleEntity)
    article_id: number;

    @Field(() => Int)
    @IsInt()
    @IdExists(FileEntity)
    video_id: number;

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsInt({ each: true })
    file_ids?: number[];

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    file_comments?: any;
}