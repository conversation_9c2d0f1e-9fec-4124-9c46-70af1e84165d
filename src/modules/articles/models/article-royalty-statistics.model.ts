import { Field, Int, ObjectType } from '@nestjs/graphql';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';

@ObjectType()
export class ArticleRoyaltyArticleTypeModel {
    @Field(() => Int)
    article_type_id: ArticleTypes;

    @Field(() => Int)
    total_final_royalty: number;
}

@ObjectType()
export class ArticleRoyaltyStatisticsModel {
    @Field(() => Int)
    user_id: number;

    @Field(() => [ArticleRoyaltyArticleTypeModel])
    article_types: ArticleRoyaltyArticleTypeModel[];
}
