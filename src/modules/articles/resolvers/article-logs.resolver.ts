import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleLogEntity } from '../../../entities/article-log.entity';
import { ArticleLogsService } from '../services/article-logs.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleLogsModel } from '../models/article-logs.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleLogSaveInputDto } from '../dtos/article-log-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';

@AuthResolver(ArticleLogEntity)
export class ArticleLogsResolver {
    constructor(
        private readonly articleLogsService: ArticleLogsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleLogEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => WorkflowEntity, { nullable: true })
    async oldWorkflow(@Parent() parent: ArticleLogEntity): Promise<WorkflowEntity | null> {
        if (!parent.old_workflow_id) return null;
        return this.dataLoader.relationBatchOne(WorkflowEntity).load(parent.old_workflow_id);
    }

    @ResolveField(() => WorkflowEntity, { nullable: true })
    async newWorkflow(@Parent() parent: ArticleLogEntity): Promise<WorkflowEntity | null> {
        if (!parent.new_workflow_id) return null;
        return this.dataLoader.relationBatchOne(WorkflowEntity).load(parent.new_workflow_id);
    }

    @Query(() => ArticleLogsModel, { name: 'article_logs_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleLogEntity>> {
        return this.articleLogsService.search(body);
    }

    @Query(() => ArticleLogEntity, { name: 'article_logs_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleLogEntity> {
        return this.articleLogsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleLogEntity, { name: 'article_logs_create' })
    async store(@Args('body') body: ArticleLogSaveInputDto, @AuthUser() auth: UserEntity): Promise<ArticleLogEntity> {
        return this.articleLogsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleLogEntity, { name: 'article_logs_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleLogSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleLogEntity> {
        return this.articleLogsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_logs_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleLogsService.softDelete(id, auth.id);
        return true;
    }
}
