import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleRoyaltyGroupEntity } from '../../../entities/article-royalty-group.entity';
import { ArticleRoyaltyGroupsService } from '../services/article-royalty-groups.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleRoyaltyGroupsModel } from '../models/article-royalty-groups.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleRoyaltyGroupSaveInputDto } from '../dtos/article-royalty-group-save-input.dto';
import { ArticleRoyaltyStatisticsInputDto } from '../dtos/article-royalty-statistics-input.dto';
import { ArticleRoyaltyStatisticsModel } from '../models/article-royalty-statistics.model';
import { ArticleEntity } from '../../../entities/article.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';

@AuthResolver(ArticleRoyaltyGroupEntity)
export class ArticleRoyaltyGroupsResolver {
    constructor(
        private readonly articleRoyaltyGroupsService: ArticleRoyaltyGroupsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleRoyaltyGroupEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: ArticleRoyaltyGroupEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleRoyaltyGroupEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => WorkflowEntity, { nullable: true })
    async workflow(@Parent() parent: ArticleRoyaltyGroupEntity): Promise<WorkflowEntity | null> {
        if (!parent.workflow_id) return null;
        return this.dataLoader.relationBatchOne(WorkflowEntity).load(parent.workflow_id);
    }

    @ResolveField(() => [ArticleRoyaltyEntity], { nullable: true })
    async articleRoyalties(@Parent() parent: ArticleRoyaltyGroupEntity): Promise<ArticleRoyaltyEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleRoyaltyEntity, 'group').load(parent.id);
    }

    @Query(() => ArticleRoyaltyGroupsModel, { name: 'article_royalty_groups_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleRoyaltyGroupEntity>> {
        return this.articleRoyaltyGroupsService.search(body);
    }

    @Query(() => ArticleRoyaltyGroupEntity, { name: 'article_royalty_groups_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleRoyaltyGroupEntity> {
        return this.articleRoyaltyGroupsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleRoyaltyGroupEntity, { name: 'article_royalty_groups_create' })
    async store(
        @Args('body') body: ArticleRoyaltyGroupSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyGroupEntity> {
        if (body.article_royalties && body.article_royalties.length > 0) {
            return this.articleRoyaltyGroupsService.createWithNested(body, auth.id);
        }
        return this.articleRoyaltyGroupsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleRoyaltyGroupEntity, { name: 'article_royalty_groups_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleRoyaltyGroupSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyGroupEntity> {
        if (body.article_royalties !== undefined) {
            return this.articleRoyaltyGroupsService.updateWithNested(id, body, auth.id);
        }
        return this.articleRoyaltyGroupsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_royalty_groups_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleRoyaltyGroupsService.softDelete(id, auth.id);
        return true;
    }

    @Query(() => [ArticleRoyaltyStatisticsModel], { name: 'article_royalty_groups_statistics' })
    async statistics(@Args('body') body: ArticleRoyaltyStatisticsInputDto): Promise<ArticleRoyaltyStatisticsModel[]> {
        return this.articleRoyaltyGroupsService.getStatistics(body);
    }
}
