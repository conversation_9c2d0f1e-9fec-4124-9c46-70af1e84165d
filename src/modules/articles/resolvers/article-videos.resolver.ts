import { Args, Mutation, Parent, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleVideoEntity } from '../../../entities/article-video.entity';
import { ArticleEntity } from '../../../entities/article.entity';
import { FileEntity } from '../../../entities/file.entity';
import { ArticleVideosService } from '../services/article-videos.service';
import { ArticleVideoSaveInputDto } from '../dtos/article-video-save-input.dto';
import { ArticleVideoCapturedFrameInputDto } from '../dtos/article-video-captured-frame-input.dto';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AuthUser } from '../../auth/auth.decorator';
import { ArticleVideoCommentUpdateInputDto } from '../dtos/article-video-comment-update-input.dto';

@AuthResolver(ArticleVideoEntity)
export class ArticleVideosResolver {
    constructor(
        private readonly articleVideosService: ArticleVideosService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleVideoEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async video(@Parent() parent: ArticleVideoEntity): Promise<FileEntity | null> {
        if (!parent.video_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.video_id);
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_create' })
    async store(@Args('body') body: ArticleVideoSaveInputDto, @AuthUser() auth: UserEntity): Promise<ArticleVideoEntity> {
        return this.articleVideosService.createWithArticleUpdate(body, auth.id);
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_update_comment' })
    async updateFileComment(
        @Args('body') body: ArticleVideoCommentUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        const articleVideo = await this.articleVideosService.findOne({
            where: { id: body.article_video_id }
        });
        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }
        const fileComments = articleVideo.file_comments || [];
        fileComments.push({
            image_ids: body.image_ids || [],
            comment: body.comment || '',
            created_by: auth.id,
            created_at: new Date()
        });
        return this.articleVideosService.updateOne(body.article_video_id, {
            file_comments: fileComments,
            updated_by: auth.id,
            updated_at: new Date()
        });
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_update_captured_frame' })
    async updateCapturedFrame(
        @Args('body') body: ArticleVideoCapturedFrameInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        const articleVideo = await this.articleVideosService.findOne({
            where: { id: body.article_video_id }
        });
        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }
        const capturedFrames = articleVideo.captured_frames || [];
        const existingFrameIndex = capturedFrames.findIndex((frame: any) => frame.id === body.image_id);
        if (existingFrameIndex === -1) {
            capturedFrames.push({
                image_url: body.image_url,
                image_id: body.image_id,
                timestamp: body.timestamp
            });
        }
        return this.articleVideosService.updateOne(body.article_video_id, {
            captured_frames: capturedFrames,
            updated_by: auth.id,
            updated_at: new Date()
        });
    }
}
