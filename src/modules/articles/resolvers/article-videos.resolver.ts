import { Args, Mutation, Parent, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleVideoEntity } from '../../../entities/article-video.entity';
import { ArticleEntity } from '../../../entities/article.entity';
import { FileEntity } from '../../../entities/file.entity';
import { ArticleVideosService } from '../services/article-videos.service';
import { ArticleVideoSaveInputDto } from '../dtos/article-video-save-input.dto';
import { ArticleVideoCapturedFrameInputDto } from '../dtos/article-video-captured-frame-input.dto';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AuthUser } from '../../auth/auth.decorator';
import { ArticleVideoCommentUpdateInputDto } from '../dtos/article-video-comment-update-input.dto';
import { ArticleVideoDeleteFrameInputDto } from '../dtos/article-video-delete-frame-input.dto';
import { ArticleNoteEntity } from '../../../entities/article-note.entity';

@AuthResolver(ArticleVideoEntity)
export class ArticleVideosResolver {
    constructor(
        private readonly articleVideosService: ArticleVideosService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleVideoEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async video(@Parent() parent: ArticleVideoEntity): Promise<FileEntity | null> {
        if (!parent.video_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.video_id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleNoteEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => [UserEntity], { nullable: true })
    async commentUsers(@Parent() parent: ArticleVideoEntity): Promise<UserEntity[]> {
        if (!parent.file_comments) return [];
        const userIds = new Set<number>();
        Object.values(parent.file_comments).forEach((comment: any) => {
            if (comment?.created_by) {
                userIds.add(comment.created_by);
            }
        });
        if (userIds.size === 0) return [];
        const users = await Promise.all(
            Array.from(userIds).map((userId) => this.dataLoader.relationBatchOne(UserEntity).load(userId))
        );
        return users.filter((user) => user !== null);
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_create' })
    async store(
        @Args('body') body: ArticleVideoSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        return this.articleVideosService.createWithArticleUpdate(body, auth.id);
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_update_comment' })
    async updateFileComment(
        @Args('body') body: ArticleVideoCommentUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        const articleVideo = await this.articleVideosService.findOne({
            where: { id: body.article_video_id },
        });
        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }
        const fileComments = articleVideo.file_comments || [];
        fileComments.push({
            image_ids: body.image_ids || [],
            comment: body.comment || '',
            created_by: auth.id,
            created_at: new Date(),
        });
        return this.articleVideosService.updateOne(body.article_video_id, {
            file_comments: fileComments,
            updated_by: auth.id,
            updated_at: new Date(),
        });
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_update_captured_frame' })
    async updateCapturedFrame(
        @Args('body') body: ArticleVideoCapturedFrameInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        const articleVideo = await this.articleVideosService.findOne({
            where: { id: body.article_video_id },
        });
        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }
        const capturedFrames = articleVideo.captured_frames || [];
        const existingFrameIndex = capturedFrames.findIndex((frame: any) => frame.id === body.image_id);
        if (existingFrameIndex === -1) {
            capturedFrames.push({
                image_url: body.image_url,
                image_id: body.image_id,
                timestamp: body.timestamp,
            });
        }
        return this.articleVideosService.updateOne(body.article_video_id, {
            captured_frames: capturedFrames,
            updated_by: auth.id,
            updated_at: new Date(),
        });
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_delete_captured_frame' })
    async deleteCapturedFrame(
        @Args('body') body: ArticleVideoDeleteFrameInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        const articleVideo = await this.articleVideosService.findOne({
            where: { id: body.article_video_id },
        });
        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }
        const capturedFrames = articleVideo.captured_frames || [];
        const updatedFrames = capturedFrames.filter((frame: any) => frame.image_id !== body.image_id);
        return this.articleVideosService.updateOne(body.article_video_id, {
            captured_frames: updatedFrames,
            updated_by: auth.id,
            updated_at: new Date(),
        });
    }
}
