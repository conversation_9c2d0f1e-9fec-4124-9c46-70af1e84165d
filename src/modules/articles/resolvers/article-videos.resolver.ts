import { Args, Mutation, Parent, ResolveField } from '@nestjs/graphql';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleVideoEntity } from '../../../entities/article-video.entity';
import { ArticleEntity } from '../../../entities/article.entity';
import { FileEntity } from '../../../entities/file.entity';
import { ArticleVideosService } from '../services/article-videos.service';
import { ArticleVideoSaveInputDto } from '../dtos/article-video-save-input.dto';
import { ArticleVideoCommentUpdateInputDto } from '../dtos/article-video-comment-update-input.dto';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { AuthUser } from '../../auth/auth.decorator';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';

@AuthResolver(ArticleVideoEntity)
export class ArticleVideosResolver {
    constructor(
        private readonly articleVideosService: ArticleVideosService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleVideoEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async video(@Parent() parent: ArticleVideoEntity): Promise<FileEntity | null> {
        if (!parent.video_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.video_id);
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_create' })
    async store(
        @Args('body') body: ArticleVideoSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        return this.articleVideosService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleVideoEntity, { name: 'article_videos_update_comment' })
    async updateFileComment(
        @Args('body') body: ArticleVideoCommentUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleVideoEntity> {
        const articleVideo = await this.articleVideosService.findOne({
            where: { id: body.article_video_id },
        });
        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }
        const fileComments = articleVideo.file_comments || [];
        const fileIds = articleVideo.file_ids || [];
        if (!fileIds.includes(body.file_id)) {
            fileIds.push(body.file_id);
        }
        const index = fileComments.findIndex((comment) => comment.file_id === body.file_id);
        if (index === -1) {
            fileComments.push({
                file_id: body.file_id,
                comments: [
                    {
                        comment: body.comment,
                        time: body.time || null,
                        created_by: auth.id,
                        created_at: new Date(),
                    },
                ],
            });
        } else {
            fileComments[index].comments.push({
                comment: body.comment,
                time: body.time || null,
                created_by: auth.id,
                created_at: new Date(),
            });
        }
        return this.articleVideosService.updateOne(body.article_video_id, {
            file_ids: fileIds,
            file_comments: fileComments,
            updated_by: auth.id,
        });
    }
}
