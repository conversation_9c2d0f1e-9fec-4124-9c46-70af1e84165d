import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>sol<PERSON><PERSON><PERSON>, Resolver } from '@nestjs/graphql';
import { AuthMutation, StaticAuthQuery } from '../../../commons/decorators/graphql.decorators';
import { ArticleEntity } from '../../../entities/article.entity';
import { ArticlesService } from '../services/articles.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticlesModel } from '../models/articles.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleSaveInputDto } from '../dtos/article-save-input.dto';
import { FileEntity } from '../../../entities/file.entity';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { LayoutEntity } from '../../../entities/layout.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { ArticleCategoryEntity } from '../../../entities/article-category.entity';
import { ArticleTagEntity } from '../../../entities/article-tag.entity';
import { ArticleFileEntity } from '../../../entities/article-file.entity';
import { ArticleNoteEntity } from '../../../entities/article-note.entity';
import { ArticleCommentEntity } from '../../../entities/article-comment.entity';
import { ArticleLogEntity } from '../../../entities/article-log.entity';
import { RelatedArticleEntity } from '../../../entities/related-article.entity';
import { ArticleArticleKindEntity } from '../../../entities/article-article-kind.entity';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { IssueEntity } from '../../../entities/issue.entity';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { ArticleRoyaltyGroupEntity } from '../../../entities/article-royalty-group.entity';
import { ArticleChangeTypesettingStatusInputDto } from '../dtos/article-change-typesetting-status-input.dto';
import { ArticleCloneInputDto } from '../dtos/article-clone-input.dto';

@Resolver(() => ArticleEntity)
export class ArticlesResolver {
    constructor(
        private readonly articlesService: ArticlesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar1(@Parent() parent: ArticleEntity): Promise<FileEntity | null> {
        if (!parent.avatar1_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.avatar1_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar2(@Parent() parent: ArticleEntity): Promise<FileEntity | null> {
        if (!parent.avatar2_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.avatar2_id);
    }

    @ResolveField(() => WorkflowEntity)
    async workflow(@Parent() parent: ArticleEntity): Promise<WorkflowEntity | null> {
        if (!parent.workflow_id) return null;
        return this.dataLoader.relationBatchOne(WorkflowEntity).load(parent.workflow_id);
    }

    @ResolveField(() => PseudonymsEntity)
    async pseudonym(@Parent() parent: ArticleEntity): Promise<PseudonymsEntity | null> {
        if (!parent.pseudonym_id) return null;
        return this.dataLoader.relationBatchOne(PseudonymsEntity).load(parent.pseudonym_id);
    }

    @ResolveField(() => DepartmentEntity)
    async department(@Parent() parent: ArticleEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() parent: ArticleEntity): Promise<FileEntity | null> {
        if (!parent.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.file_id);
    }

    @ResolveField(() => ArticleEntity, { nullable: true })
    async rootArticle(@Parent() parent: ArticleEntity): Promise<ArticleEntity | null> {
        if (!parent.root_article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.root_article_id);
    }

    @ResolveField(() => [ArticleEntity], { nullable: true })
    async childrenArticles(@Parent() parent: ArticleEntity): Promise<ArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleEntity, 'root_article').load(parent.id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async lockUser(@Parent() parent: ArticleEntity): Promise<UserEntity | null> {
        if (!parent.lock_user_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.lock_user_id);
    }

    @ResolveField(() => [ArticleArticleKindEntity], { nullable: true })
    async articleKinds(@Parent() parent: ArticleEntity): Promise<ArticleArticleKindEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleArticleKindEntity, 'article').load(parent.id);
    }

    @ResolveField(() => [ArticleCategoryEntity], { nullable: true })
    async articleCategories(@Parent() parent: ArticleEntity): Promise<ArticleCategoryEntity[]> {
        // Use query builder to ensure we only get categories that exist and aren't deleted
        return this.articlesService.repo.manager
            .createQueryBuilder(ArticleCategoryEntity, 'ac')
            .leftJoinAndSelect('ac.category', 'category')
            .where('ac.article_id = :articleId', { articleId: parent.id })
            .andWhere('category.id IS NOT NULL') // Ensure category exists
            .orderBy('ac.display_order', 'ASC')
            .getMany();
    }

    @ResolveField(() => [ArticleTagEntity], { nullable: true })
    async articleTags(@Parent() parent: ArticleEntity): Promise<ArticleTagEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleTagEntity, 'article').load(parent.id);
    }

    @ResolveField(() => [ArticleFileEntity], { nullable: true })
    async articleFiles(@Parent() parent: ArticleEntity): Promise<ArticleFileEntity[]> {
        // Get both deleted and non-deleted article files using query builder
        return this.articlesService.repo.manager
            .createQueryBuilder(ArticleFileEntity, 'af')
            .leftJoinAndSelect('af.file', 'file')
            .where('af.article_id = :articleId', { articleId: parent.id })
            .withDeleted() // This explicitly includes soft-deleted records
            .orderBy('af.created_at', 'DESC')
            .getMany();
    }

    @ResolveField(() => [ArticleNoteEntity], { nullable: true })
    async articleNotes(@Parent() parent: ArticleEntity): Promise<ArticleNoteEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleNoteEntity, 'article').load(parent.id);
    }

    @ResolveField(() => [ArticleCommentEntity], { nullable: true })
    async articleComments(@Parent() parent: ArticleEntity): Promise<ArticleCommentEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleCommentEntity, 'article').load(parent.id);
    }

    @ResolveField(() => [ArticleLogEntity], { nullable: true })
    async articleLogs(@Parent() parent: ArticleEntity): Promise<ArticleLogEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleLogEntity, 'article').load(parent.id);
    }

    @ResolveField(() => [RelatedArticleEntity], { nullable: true })
    async relatedArticles(@Parent() parent: ArticleEntity): Promise<RelatedArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(RelatedArticleEntity, 'article').load(parent.id);
    }

    @ResolveField(() => [RelatedArticleEntity], { nullable: true })
    async relatedToArticles(@Parent() parent: ArticleEntity): Promise<RelatedArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(RelatedArticleEntity, 'relatedArticle').load(parent.id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleNoteEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: ArticleNoteEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @ResolveField(() => LayoutEntity, { nullable: true })
    async articleWebLayout(@Parent() parent: ArticleEntity): Promise<LayoutEntity | null> {
        if (!parent.web_layout_id) return null;
        return this.dataLoader.relationBatchOne(LayoutEntity).load(parent.web_layout_id);
    }

    @ResolveField(() => LayoutEntity, { nullable: true })
    async articleMobileLayout(@Parent() parent: ArticleEntity): Promise<LayoutEntity | null> {
        if (!parent.mobile_layout_id) return null;
        return this.dataLoader.relationBatchOne(LayoutEntity).load(parent.mobile_layout_id);
    }

    @ResolveField(() => PressPublicationEntity, { nullable: true })
    async pressPublication(@Parent() parent: ArticleEntity): Promise<PressPublicationEntity | null> {
        if (!parent.press_publication_id) return null;
        return this.dataLoader.relationBatchOne(PressPublicationEntity).load(parent.press_publication_id);
    }

    @ResolveField(() => IssueEntity, { nullable: true })
    async issue(@Parent() parent: ArticleEntity): Promise<IssueEntity | null> {
        if (!parent.issue_id) return null;
        return this.dataLoader.relationBatchOne(IssueEntity).load(parent.issue_id);
    }

    @ResolveField(() => [ArticleIssuePageEntity], { nullable: true })
    async articleIssuePages(@Parent() parent: ArticleEntity): Promise<ArticleIssuePageEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleIssuePageEntity, 'article').load(parent.id);
    }

    @ResolveField(() => ArticleRoyaltyGroupEntity, { nullable: true })
    async articleRoyaltyGroup(@Parent() parent: ArticleEntity): Promise<ArticleRoyaltyGroupEntity | null> {
        const groups = await this.dataLoader.relationBatchOneMany(ArticleRoyaltyGroupEntity, 'article').load(parent.id);
        return groups?.length > 0 ? groups[0] : null;
    }

    /* End ResolveField */

    @StaticAuthQuery(() => ArticlesModel, { name: 'articles_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleEntity>> {
        return this.articlesService.search(body);
    }

    @StaticAuthQuery(() => ArticleEntity, { name: 'articles_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleEntity> {
        return this.articlesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @AuthMutation(() => ArticleEntity, { name: 'articles_create' })
    async store(@Args('body') body: ArticleSaveInputDto, @AuthUser() auth: UserEntity): Promise<ArticleEntity> {
        return this.articlesService.saveArticle(body, auth.id);
    }

    @AuthMutation(() => ArticleEntity, { name: 'articles_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleEntity> {
        return this.articlesService.saveArticle({ ...body, id }, auth.id);
    }

    @AuthMutation(() => Boolean, { name: 'articles_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articlesService.softDelete(id, auth.id);
        return true;
    }

    @AuthMutation(() => Boolean, { name: 'articles_change_lock' })
    async changeLock(
        @Args('id', { type: () => Int }) id: number,
        @Args('lock', { type: () => Boolean }) lock: boolean,
        @AuthUser() auth: UserEntity
    ): Promise<boolean> {
        await this.articlesService.changeLock(id, auth.id, lock);
        return true;
    }

    @AuthMutation(() => Boolean, { name: 'articles_change_typesetting_status' })
    async changeTypesettingStatus(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleChangeTypesettingStatusInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<boolean> {
        await this.articlesService.updateOne(id, { ...body, updated_by: auth.id });
        return true;
    }

    @AuthMutation(() => ArticleEntity, { name: 'articles_clone' })
    async clone(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleCloneInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleEntity> {
        return this.articlesService.cloneArticle(id, body, auth.id);
    }
}
