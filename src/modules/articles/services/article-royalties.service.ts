import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';

@Injectable()
export class ArticleRoyaltiesService extends BaseService<ArticleRoyaltyEntity> {
    constructor(@InjectRepository(ArticleRoyaltyEntity) public readonly repo: Repository<ArticleRoyaltyEntity>) {
        super(repo);
    }
}
