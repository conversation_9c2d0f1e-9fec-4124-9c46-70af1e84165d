import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, IsNull, Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleRoyaltyGroupEntity } from '../../../entities/article-royalty-group.entity';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { ArticleEntity } from '../../../entities/article.entity';
import {
    ArticleRoyaltyGroupSaveInputDto,
    ArticleRoyaltyNestedInput,
    ArticleRoyaltyUserNestedInput,
} from '../dtos/article-royalty-group-save-input.dto';
import { ArticleRoyaltyStatisticsInputDto } from '../dtos/article-royalty-statistics-input.dto';
import { ArticleRoyaltyStatisticsModel } from '../models/article-royalty-statistics.model';

@Injectable()
export class ArticleRoyaltyGroupsService extends BaseService<ArticleRoyaltyGroupEntity> {
    constructor(
        @InjectRepository(ArticleRoyaltyGroupEntity)
        public readonly repo: Repository<ArticleRoyaltyGroupEntity>
    ) {
        super(repo);
    }

    /**
     * Create article royalty group with nested royalties and users
     */
    async createWithNested(data: ArticleRoyaltyGroupSaveInputDto, userId: number): Promise<ArticleRoyaltyGroupEntity> {
        return this.repo.manager.transaction(async (manager) => {
            // Determine publish_date based on article_id
            let publishDate: Date;

            if (data.article_id && data.article_id > 0) {
                // If article_id > 0, try to get article's publish_date
                const article = await manager.findOne(ArticleEntity, {
                    where: { id: data.article_id },
                    select: ['id', 'publish_date'],
                });

                if (article?.publish_date) {
                    publishDate = article.publish_date;
                } else {
                    publishDate = new Date();
                }
            } else {
                // If article_id is NULL, set to current date
                publishDate = new Date();
            }

            // Create the royalty group
            const groupData = {
                article_id: data.article_id,
                title: data.title,
                workflow_id: data.workflow_id,
                article_type_id: data.article_type_id,
                publish_date: publishDate,
                created_by: userId,
                created_at: new Date(),
            };

            const group = await manager.save(ArticleRoyaltyGroupEntity, groupData);

            // Handle nested royalties if provided
            if (data.article_royalties && data.article_royalties.length > 0) {
                await this.handleNestedRoyalties(manager, group.id, data.article_royalties, userId);
            }

            // Return the created group with relations
            return manager.findOne(ArticleRoyaltyGroupEntity, {
                where: { id: group.id },
                relations: ['article', 'workflow', 'articleRoyalties', 'articleRoyalties.articleRoyaltyUsers'],
            }) as Promise<ArticleRoyaltyGroupEntity>;
        });
    }

    /**
     * Update article royalty group with nested royalties and users
     */
    async updateWithNested(
        id: number,
        data: ArticleRoyaltyGroupSaveInputDto,
        userId: number
    ): Promise<ArticleRoyaltyGroupEntity> {
        return this.repo.manager.transaction(async (manager) => {
            // Check if group exists
            const existingGroup = await manager.findOne(ArticleRoyaltyGroupEntity, { where: { id } });
            if (!existingGroup) {
                throw new NotFoundException(`Article royalty group with ID ${id} not found`);
            }

            // Update the royalty group
            const groupData = {
                article_id: data.article_id,
                title: data.title,
                workflow_id: data.workflow_id,
                article_type_id: data.article_type_id,
                updated_by: userId,
                updated_at: new Date(),
            };

            await manager.update(ArticleRoyaltyGroupEntity, id, groupData);

            // Handle nested royalties if provided
            if (data.article_royalties) {
                await this.handleNestedRoyalties(manager, id, data.article_royalties, userId);
            }

            // Return the updated group with relations
            return manager.findOne(ArticleRoyaltyGroupEntity, {
                where: { id },
                relations: ['article', 'workflow', 'articleRoyalties', 'articleRoyalties.articleRoyaltyUsers'],
            }) as Promise<ArticleRoyaltyGroupEntity>;
        });
    }

    /**
     * Handle nested royalties for a group
     */
    private async handleNestedRoyalties(
        manager: EntityManager,
        groupId: number,
        royaltiesData: ArticleRoyaltyNestedInput[],
        userId: number
    ): Promise<void> {
        // Get existing royalties for this group
        const existingRoyalties = await manager.find(ArticleRoyaltyEntity, {
            where: { group_id: groupId, deleted_at: IsNull() },
        });

        const inputRoyaltyIds = royaltiesData.filter((r) => r.id).map((r) => r.id!);

        // Find royalties to soft delete (exist in DB but not in input)
        const royaltiesToDelete = existingRoyalties.filter((r) => !inputRoyaltyIds.includes(r.id));

        // Soft delete removed royalties
        if (royaltiesToDelete.length > 0) {
            // First, soft delete all royalty users for these royalties
            const royaltyIdsToDelete = royaltiesToDelete.map((r) => r.id);
            await manager.update(
                ArticleRoyaltyUserEntity,
                { article_royalty_id: In(royaltyIdsToDelete) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );

            // Then soft delete the royalties
            await manager.update(
                ArticleRoyaltyEntity,
                { id: In(royaltyIdsToDelete) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );
        }

        // Process each royalty in input
        for (const royaltyData of royaltiesData) {
            let royaltyId: number;

            if (royaltyData.id) {
                // Update existing royalty
                const existingRoyalty = existingRoyalties.find((r) => r.id === royaltyData.id);
                if (existingRoyalty) {
                    await manager.update(ArticleRoyaltyEntity, royaltyData.id, {
                        type_id: royaltyData.type_id,
                        royalty_type_id: royaltyData.royalty_type_id,
                        suggest_royalty: royaltyData.suggest_royalty,
                        title: royaltyData.title,
                        article_statistic: royaltyData.article_statistic,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                    royaltyId = royaltyData.id;
                } else {
                    continue; // Skip if royalty doesn't exist
                }
            } else {
                // Create new royalty
                const newRoyalty = await manager.save(ArticleRoyaltyEntity, {
                    group_id: groupId,
                    type_id: royaltyData.type_id,
                    royalty_type_id: royaltyData.royalty_type_id,
                    suggest_royalty: royaltyData.suggest_royalty,
                    title: royaltyData.title,
                    article_statistic: royaltyData.article_statistic,
                    created_by: userId,
                    created_at: new Date(),
                });
                royaltyId = newRoyalty.id;
            }

            // Handle nested royalty users if provided
            if (royaltyData.article_royalty_users) {
                await this.handleNestedRoyaltyUsers(manager, royaltyId, royaltyData.article_royalty_users, userId);
            }
        }
    }

    /**
     * Handle nested royalty users for a specific royalty
     */
    private async handleNestedRoyaltyUsers(
        manager: EntityManager,
        royaltyId: number,
        usersData: ArticleRoyaltyUserNestedInput[],
        userId: number
    ): Promise<void> {
        // Get existing royalty users
        const existingUsers = await manager.find(ArticleRoyaltyUserEntity, {
            where: { article_royalty_id: royaltyId, deleted_at: IsNull() },
        });

        const inputUserIds = usersData.filter((u) => u.id).map((u) => u.id!);

        // Find users to soft delete (exist in DB but not in input)
        const usersToDelete = existingUsers.filter((u) => !inputUserIds.includes(u.id));

        // Soft delete removed users
        if (usersToDelete.length > 0) {
            await manager.update(
                ArticleRoyaltyUserEntity,
                { id: In(usersToDelete.map((u) => u.id)) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );
        }

        // Process each user in input
        for (const userData of usersData) {
            if (userData.id) {
                // Update existing user
                const existingUser = existingUsers.find((u) => u.id === userData.id);
                if (existingUser) {
                    await manager.update(ArticleRoyaltyUserEntity, userData.id, {
                        user_id: userData.user_id,
                        percent: userData.percent,
                        comment: userData.comment,
                        param_config: userData.param_config,
                        final_royalty: userData.final_royalty,
                        suggest_royalty: userData.suggest_royalty,
                        file_id: userData.file_id,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                }
            } else {
                // Create new user
                await manager.save(ArticleRoyaltyUserEntity, {
                    article_royalty_id: royaltyId,
                    user_id: userData.user_id,
                    percent: userData.percent,
                    comment: userData.comment,
                    param_config: userData.param_config,
                    final_royalty: userData.final_royalty ?? 0,
                    suggest_royalty: userData.suggest_royalty ?? 0,
                    file_id: userData.file_id,
                    created_by: userId,
                    created_at: new Date(),
                });
            }
        }
    }

    async getStatistics(data: ArticleRoyaltyStatisticsInputDto): Promise<ArticleRoyaltyStatisticsModel[]> {
        const queryBuilder = this.repo
            .createQueryBuilder('arg')
            .select([
                'aru.user_id as user_id',
                'arg.article_type_id as article_type_id',
                'SUM(aru.final_royalty) as total_final_royalty',
            ])
            .innerJoin('arg.articleRoyalties', 'ar')
            .innerJoin('ar.articleRoyaltyUsers', 'aru')
            .where('arg.publish_date >= :start_date', { start_date: data.start_date })
            .andWhere('arg.publish_date <= :end_date', { end_date: data.end_date })
            .groupBy('aru.user_id, arg.article_type_id')
            .orderBy('aru.user_id, arg.article_type_id');

        if (data.user_ids && data.user_ids.length > 0) {
            queryBuilder.andWhere('aru.user_id IN (:...user_ids)', { user_ids: data.user_ids });
        }

        const results = await queryBuilder.getRawMany();

        // Group by user_id
        const grouped: Record<number, { article_type_id: number; total_final_royalty: number }[]> = {};
        for (const result of results) {
            const userId = parseInt(result.user_id);
            if (!grouped[userId]) {
                grouped[userId] = [];
            }
            grouped[userId].push({
                article_type_id: parseInt(result.article_type_id),
                total_final_royalty: parseInt(result.total_final_royalty) || 0,
            });
        }

        // Format as requested
        return Object.entries(grouped).map(([user_id, article_types]) => ({
            user_id: parseInt(user_id),
            article_types,
        }));
    }
}
