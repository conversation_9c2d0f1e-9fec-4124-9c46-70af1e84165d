import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleVideoEntity } from '../../../entities/article-video.entity';
import { ArticleEntity } from '../../../entities/article.entity';
import { ArticleVideoSaveInputDto } from '../dtos/article-video-save-input.dto';

@Injectable()
export class ArticleVideosService extends BaseService<ArticleVideoEntity> {
    constructor(
        @InjectRepository(ArticleVideoEntity)
        protected readonly repo: Repository<ArticleVideoEntity>,
        @InjectRepository(ArticleEntity)
        private readonly articleRepo: Repository<ArticleEntity>
    ) {
        super(repo);
    }

    async createWithArticleUpdate(
        data: ArticleVideoSaveInputDto,
        userId: number
    ): Promise<ArticleVideoEntity> {
        return this.repo.manager.transaction(async (manager) => {
            const articleVideo = manager.create(ArticleVideoEntity, {
                ...data,
                created_by: userId,
                created_at: new Date()
            });
            const savedArticleVideo = await manager.save(articleVideo);
            if (data.article_id && data.video_id) {
                await manager.update(ArticleEntity, data.article_id, {
                    video_id: data.video_id,
                    updated_by: userId,
                    updated_at: new Date()
                });
            }
            return savedArticleVideo;
        });
    }
}