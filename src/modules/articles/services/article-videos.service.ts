import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleVideoEntity } from '../../../entities/article-video.entity';
import { ArticleEntity } from '../../../entities/article.entity';
import { ArticleVideoSaveInputDto } from '../dtos/article-video-save-input.dto';
import { ArticleVideoDeleteFrameInputDto } from '../dtos/article-video-delete-frame-input.dto';
import { NotFoundException } from '@nestjs/common';

@Injectable()
export class ArticleVideosService extends BaseService<ArticleVideoEntity> {
    constructor(
        @InjectRepository(ArticleVideoEntity)
        protected readonly repo: Repository<ArticleVideoEntity>,
        @InjectRepository(ArticleEntity)
        private readonly articleRepo: Repository<ArticleEntity>
    ) {
        super(repo);
    }

    async createWithArticleUpdate(
        data: ArticleVideoSaveInputDto,
        userId: number
    ): Promise<ArticleVideoEntity> {
        return this.repo.manager.transaction(async (manager) => {
            const articleVideo = manager.create(ArticleVideoEntity, {
                ...data,
                created_by: userId,
                created_at: new Date()
            });
            const savedArticleVideo = await manager.save(articleVideo);
            if (data.article_id && data.video_id) {
                await manager.update(ArticleEntity, data.article_id, {
                    video_id: data.video_id,
                    updated_by: userId,
                    updated_at: new Date()
                });
            }
            return savedArticleVideo;
        });
    }

    /**
     * Delete captured frame by image_id from captured_frames array
     */
    async deleteCapturedFrame(
        data: ArticleVideoDeleteFrameInputDto,
        userId: number
    ): Promise<ArticleVideoEntity> {
        // Find the article video
        const articleVideo = await this.repo.findOne({
            where: { id: data.article_video_id }
        });

        if (!articleVideo) {
            throw new NotFoundException('Article video not found');
        }

        // Get current captured_frames or initialize as empty array
        const capturedFrames = articleVideo.captured_frames || [];

        // Filter out the frame with the matching image_id
        const updatedFrames = capturedFrames.filter((frame: any) => frame.id !== data.image_id);

        // Update the entity with the modified captured_frames array
        await this.repo.update(data.article_video_id, {
            captured_frames: updatedFrames,
            updated_by: userId,
            updated_at: new Date()
        });

        // Return the updated entity
        return this.repo.findOneOrFail({
            where: { id: data.article_video_id }
        });
    }
}
