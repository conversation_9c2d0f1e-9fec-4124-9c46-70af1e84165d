import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleVideoEntity } from '../../../entities/article-video.entity';

@Injectable()
export class ArticleVideosService extends BaseService<ArticleVideoEntity> {
    constructor(
        @InjectRepository(ArticleVideoEntity)
        protected readonly repo: Repository<ArticleVideoEntity>
    ) {
        super(repo);
    }
}