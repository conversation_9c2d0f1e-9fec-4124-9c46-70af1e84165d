import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleEntity } from '../../../entities/article.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, IsNull, QueryRunner, Repository } from 'typeorm';
import { ArticleRoyaltyCreateInput, ArticleSaveInputDto } from '../dtos/article-save-input.dto';
import { ArticleArticleKindEntity } from '../../../entities/article-article-kind.entity';
import slugify from 'slugify';
import { ArticleCategoriesService } from './article-categories.service';
import { ItemWorkflowType, WorkflowEntity, WorkflowType } from '../../../entities/workflow.entity';
import { ArticleFileEntity } from '../../../entities/article-file.entity';
import { FileEntity } from '../../../entities/file.entity';
import appConf from '../../../configs/app.conf';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { BusinessException } from '../../../commons/exceptions/business.exception';
import { ItemStatus } from '../../../commons/enums.common';
import { RelatedArticleEntity } from '../../../entities/related-article.entity';
import { ArticleTagEntity } from '../../../entities/article-tag.entity';
import { TagEntity } from '../../../entities/tag.entity';
import { ArticleNoteEntity } from '../../../entities/article-note.entity';
import { ArticleCategoryEntity } from '../../../entities/article-category.entity';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { ArticleCloneInputDto } from '../dtos/article-clone-input.dto';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltyGroupEntity } from '../../../entities/article-royalty-group.entity';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { ArticleLogEntity } from '../../../entities/article-log.entity';
import axios from 'axios';

@Injectable()
export class ArticlesService extends BaseService<ArticleEntity> {
    constructor(
        private readonly articleCategoriesService: ArticleCategoriesService,
        @InjectRepository(ArticleEntity) public readonly repo: Repository<ArticleEntity>,
        @InjectDataSource() private readonly dataSource: DataSource
    ) {
        super(repo);
    }

    /**
     * Extract file URLs from HTML content based on allowed file types
     */
    private extractFileUrlsFromContent(content: string): string[] {
        if (!content) return [];

        // Get allowed file extensions from config
        const allowedExtensions = appConf.ALLOWED_FILE_TYPES.join('|');

        // Enhanced regex to match various file URL patterns
        const fileRegex = new RegExp(
            `<(?:img|a|video|audio|source)[^>]*(?:src|href)\\s*=\\s*["']([^"']+\\.(?:${allowedExtensions}))(?:\\?[^"']*)?["'][^>]*>`,
            'gi'
        );

        // Also match direct file URLs in text content
        const directUrlRegex = new RegExp(`https?://[^\\s<>"']+\\.(?:${allowedExtensions})(?:\\?[^\\s<>"']*)?`, 'gi');

        const urls: string[] = [];
        let match: RegExpExecArray | null;

        // Extract from HTML tags
        while ((match = fileRegex.exec(content)) !== null) {
            if (!match[1].startsWith('http')) continue;
            urls.push(match[1]);
        }

        // Extract direct URLs from text
        while ((match = directUrlRegex.exec(content)) !== null) {
            urls.push(match[0]);
        }

        // Remove duplicates and return
        return [...new Set(urls)];
    }

    /**
     * Convert the full URL to a relative path for database comparison
     */
    private urlToRelativePath(url: string): string {
        if (!url) return '';

        try {
            const urlObj = new URL(url);
            // Get the pathname part (e.g., /files/images/2025-06-12/1749749738750-994349352_dh2.jpg)
            let pathname = urlObj.pathname;

            // Remove the leading slash if present
            if (pathname.startsWith('/')) {
                pathname = pathname.substring(1);
            }

            // Convert files/ prefix to uploads/ for database comparison
            if (pathname.startsWith('files/')) {
                return pathname.replace('files/', 'uploads/');
            }

            return pathname;
        } catch {
            // If URL parsing fails, try simple string replacement
            const baseUrl = appConf.API_URL;
            if (url.startsWith(baseUrl)) {
                return url.replace(baseUrl, '').replace('files/', 'uploads/');
            }
            return url;
        }
    }

    /**
     * Handle article files management for all file types
     */
    private async handleArticleFiles(
        articleId: number,
        content: string,
        userId: number,
        queryRunner: QueryRunner,
        isNewArticle: boolean = false
    ): Promise<void> {
        if (!content) return;

        // Extract all file URLs from content (not just images)
        const fileUrls = this.extractFileUrlsFromContent(content);
        if (fileUrls.length === 0) return;

        // Convert URLs to relative paths for database comparison
        const relativePaths = fileUrls.map((url) => this.urlToRelativePath(url));

        // Find files in database that match these paths
        const files = await queryRunner.manager.find(FileEntity, {
            where: {
                file_url: In(relativePaths.filter((path) => path)), // Filter out empty paths
            },
        });

        const fileIds = files.map((file) => file.id);

        if (isNewArticle) {
            // For new articles, add all found files with is_royalty = true
            if (fileIds.length > 0) {
                const articleFiles = fileIds.map((fileId) => ({
                    article_id: articleId,
                    file_id: fileId,
                    is_royalty: true, // Auto set is_royalty = true for new articles
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleFileEntity, articleFiles);
            }
        } else {
            // For existing articles, manage the relationship
            // Get current article files (only non-deleted ones)
            const currentArticleFiles = await queryRunner.manager.find(ArticleFileEntity, {
                where: {
                    article_id: articleId,
                    deleted_at: IsNull(), // Only get non-deleted records
                },
            });

            const currentFileIds = currentArticleFiles.map((af) => af.file_id);

            // Find files to add (in new content but not in current relations)
            const filesToAdd = fileIds.filter((fileId) => !currentFileIds.includes(fileId));

            // Find files to remove (in current relations but not in new content)
            const filesToRemove = currentFileIds.filter((fileId) => !fileIds.includes(fileId));

            // Add new file relations
            if (filesToAdd.length > 0) {
                const newArticleFiles = filesToAdd.map((fileId) => ({
                    article_id: articleId,
                    file_id: fileId,
                    is_royalty: true, // Auto set is_royalty = true for new files added to articles
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleFileEntity, newArticleFiles);
            }

            // Soft delete removed file relations
            if (filesToRemove.length > 0) {
                await queryRunner.manager.update(
                    ArticleFileEntity,
                    {
                        article_id: articleId,
                        file_id: In(filesToRemove),
                    },
                    {
                        deleted_at: new Date(),
                        deleted_by: userId,
                    }
                );
            }
        }
    }

    /**
     * Create an article log entry
     */
    private async createArticleLog(
        articleId: number,
        action: string,
        details: any,
        userId: number,
        queryRunner: QueryRunner,
        metadata?: any,
        oldWorkflowId?: number,
        newWorkflowId?: number
    ): Promise<void> {
        const logContent = {
            action,
            timestamp: new Date().toISOString(),
            user_id: userId,
            details,
            metadata: metadata || {},
        };

        await queryRunner.manager.insert(ArticleLogEntity, {
            article_id: articleId,
            content: JSON.stringify(logContent),
            old_workflow_id: oldWorkflowId,
            new_workflow_id: newWorkflowId,
            created_by: userId,
            created_at: new Date(),
        });
    }

    /**
     * Handle article royalties management through royalty groups
     */
    private async handleArticleRoyalties(
        articleId: number,
        royalties: ArticleRoyaltyCreateInput[],
        userId: number,
        queryRunner: QueryRunner
    ): Promise<void> {
        // Get existing royalty group for this article
        const existingGroup = await queryRunner.manager.findOne(ArticleRoyaltyGroupEntity, {
            where: { article_id: articleId },
        });

        let groupId: number;

        if (existingGroup) {
            // Use existing group
            groupId = existingGroup.id;

            // Get existing royalties for this group
            const existingRoyalties = await queryRunner.manager.find(ArticleRoyaltyEntity, {
                where: { group_id: groupId },
                select: ['id'],
            });

            // Delete existing royalty users first (if any exist)
            if (existingRoyalties.length > 0) {
                const royaltyIds = existingRoyalties.map((r) => r.id);
                await queryRunner.manager.delete(ArticleRoyaltyUserEntity, {
                    article_royalty_id: In(royaltyIds),
                });
            }

            // Delete existing royalties
            await queryRunner.manager.delete(ArticleRoyaltyEntity, {
                group_id: groupId,
            });
        } else {
            // Create new royalty group for this article
            const groupToInsert = {
                article_id: articleId,
                title: 'Default Royalty Group',
                created_by: userId,
                created_at: new Date(),
            };

            const groupInsertResult = await queryRunner.manager.insert(ArticleRoyaltyGroupEntity, groupToInsert);
            groupId = groupInsertResult.identifiers[0].id;
        }

        // Process each royalty
        for (const royaltyData of royalties) {
            // Create article royalty
            const royaltyToInsert = {
                group_id: groupId,
                type_id: royaltyData.type_id,
                royalty_type_id: royaltyData.royalty_type_id,
                suggest_royalty: royaltyData.suggest_royalty,
                title: royaltyData.title,
                created_by: userId,
                created_at: new Date(),
            };

            const royaltyInsertResult = await queryRunner.manager.insert(ArticleRoyaltyEntity, royaltyToInsert);
            const royaltyId = royaltyInsertResult.identifiers[0].id;

            // Create article royalty users if provided
            if (royaltyData.article_royalty_users && royaltyData.article_royalty_users.length > 0) {
                const royaltyUsers = royaltyData.article_royalty_users.map((userRoyalty) => ({
                    article_royalty_id: royaltyId,
                    user_id: userRoyalty.user_id,
                    percent: userRoyalty.percent,
                    comment: userRoyalty.comment,
                    param_config: userRoyalty.param_config,
                    final_royalty: userRoyalty.final_royalty ?? 0,
                    suggest_royalty: userRoyalty.suggest_royalty ?? 0,
                    file_id: userRoyalty.file_id,
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleRoyaltyUserEntity, royaltyUsers);
            }
        }
    }

    /**
     * Handle article royalty group with nested royalties and users
     */
    private async handleArticleRoyaltyGroup(
        articleId: number,
        groupData: any, // ArticleRoyaltyGroupCreateInput type
        userId: number,
        queryRunner: QueryRunner
    ): Promise<void> {
        // Check if there's an existing royalty group for this article
        const existingGroup = await queryRunner.manager.findOne(ArticleRoyaltyGroupEntity, {
            where: { article_id: articleId },
        });

        let groupId: number;

        if (groupData.id || existingGroup) {
            // Update existing group
            const targetGroupId = groupData.id || existingGroup!.id;

            const groupUpdateData = {
                title: groupData.title,
                workflow_id: groupData.workflow_id,
                updated_by: userId,
                updated_at: new Date(),
            };

            await queryRunner.manager.update(ArticleRoyaltyGroupEntity, targetGroupId, groupUpdateData);
            groupId = targetGroupId;

            // Handle nested royalties if provided
            if (groupData.article_royalties) {
                await this.handleNestedRoyaltiesInGroup(queryRunner, groupId, groupData.article_royalties, userId);
            }
        } else {
            // Create new group
            // Determine publish_date based on article_id
            let publishDate: Date;

            if (articleId && articleId > 0) {
                // If article_id > 0, try to get article's publish_date
                const article = await queryRunner.manager.findOne(ArticleEntity, {
                    where: { id: articleId },
                    select: ['id', 'publish_date'],
                });

                if (article?.publish_date) {
                    publishDate = article.publish_date;
                } else {
                    publishDate = new Date();
                }
            } else {
                // If article_id is NULL, set to current date
                publishDate = new Date();
            }

            const groupToInsert = {
                article_id: articleId,
                title: groupData.title || 'Default Royalty Group',
                workflow_id: groupData.workflow_id || null,
                article_type_id: groupData.article_type_id,
                publish_date: publishDate,
                created_by: userId,
                created_at: new Date(),
            };

            console.log('🔍 Creating royalty group with data:', groupToInsert);

            const groupInsertResult = await queryRunner.manager.insert(ArticleRoyaltyGroupEntity, groupToInsert);
            groupId = groupInsertResult.identifiers[0].id;

            // Handle nested royalties if provided
            if (groupData.article_royalties) {
                await this.handleNestedRoyaltiesInGroup(queryRunner, groupId, groupData.article_royalties, userId);
            }
        }
    }

    /**
     * Handle nested royalties within a group
     */
    private async handleNestedRoyaltiesInGroup(
        queryRunner: QueryRunner,
        groupId: number,
        royaltiesData: any[], // ArticleRoyaltyCreateInput[]
        userId: number
    ): Promise<void> {
        // Get existing royalties for this group
        const existingRoyalties = await queryRunner.manager.find(ArticleRoyaltyEntity, {
            where: { group_id: groupId, deleted_at: IsNull() },
        });

        const inputRoyaltyIds = royaltiesData.filter((r) => r.id).map((r) => r.id!);

        // Find royalties to soft delete (exist in DB but not in input)
        const royaltiesToDelete = existingRoyalties.filter((r) => !inputRoyaltyIds.includes(r.id));

        // Soft delete removed royalties and their users
        if (royaltiesToDelete.length > 0) {
            const royaltyIdsToDelete = royaltiesToDelete.map((r) => r.id);

            // First, soft delete all royalty users for these royalties
            await queryRunner.manager.update(
                ArticleRoyaltyUserEntity,
                { article_royalty_id: In(royaltyIdsToDelete) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );

            // Then soft delete the royalties
            await queryRunner.manager.update(
                ArticleRoyaltyEntity,
                { id: In(royaltyIdsToDelete) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );
        }

        // Process each royalty in input
        for (const royaltyData of royaltiesData) {
            let royaltyId: number;

            if (royaltyData.id) {
                // Update existing royalty
                const existingRoyalty = existingRoyalties.find((r) => r.id === royaltyData.id);
                if (existingRoyalty) {
                    await queryRunner.manager.update(ArticleRoyaltyEntity, royaltyData.id, {
                        type_id: royaltyData.type_id,
                        royalty_type_id: royaltyData.royalty_type_id,
                        suggest_royalty: royaltyData.suggest_royalty,
                        title: royaltyData.title,
                        article_statistic: royaltyData.article_statistic,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                    royaltyId = royaltyData.id;
                } else {
                    continue; // Skip if royalty doesn't exist
                }
            } else {
                // Create new royalty
                const royaltyToInsert = {
                    group_id: groupId,
                    type_id: royaltyData.type_id,
                    royalty_type_id: royaltyData.royalty_type_id,
                    suggest_royalty: royaltyData.suggest_royalty,
                    title: royaltyData.title,
                    article_statistic: royaltyData.article_statistic,
                    created_by: userId,
                    created_at: new Date(),
                };

                const royaltyInsertResult = await queryRunner.manager.insert(ArticleRoyaltyEntity, royaltyToInsert);
                royaltyId = royaltyInsertResult.identifiers[0].id;
            }

            // Handle nested royalty users if provided
            if (royaltyData.article_royalty_users && royaltyData.article_royalty_users.length > 0) {
                await this.handleNestedRoyaltyUsersInGroup(
                    queryRunner,
                    royaltyId,
                    royaltyData.article_royalty_users,
                    userId
                );
            }
        }
    }

    /**
     * Handle nested royalty users within a royalty
     */
    private async handleNestedRoyaltyUsersInGroup(
        queryRunner: QueryRunner,
        royaltyId: number,
        usersData: any[], // ArticleRoyaltyUserCreateInput[]
        userId: number
    ): Promise<void> {
        // Get existing royalty users
        const existingUsers = await queryRunner.manager.find(ArticleRoyaltyUserEntity, {
            where: { article_royalty_id: royaltyId, deleted_at: IsNull() },
        });

        const inputUserIds = usersData.filter((u) => u.id).map((u) => u.id!);

        // Find users to soft delete (exist in DB but not in input)
        const usersToDelete = existingUsers.filter((u) => !inputUserIds.includes(u.id));

        // Soft delete removed users
        if (usersToDelete.length > 0) {
            await queryRunner.manager.update(
                ArticleRoyaltyUserEntity,
                { id: In(usersToDelete.map((u) => u.id)) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );
        }

        // Process each user in input
        for (const userData of usersData) {
            if (userData.id) {
                // Update existing user
                const existingUser = existingUsers.find((u) => u.id === userData.id);
                if (existingUser) {
                    await queryRunner.manager.update(ArticleRoyaltyUserEntity, userData.id, {
                        user_id: userData.user_id,
                        percent: userData.percent,
                        comment: userData.comment,
                        param_config: userData.param_config,
                        final_royalty: userData.final_royalty,
                        suggest_royalty: userData.suggest_royalty,
                        file_id: userData.file_id,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                }
            } else {
                // Create new user
                const userToInsert = {
                    article_royalty_id: royaltyId,
                    user_id: userData.user_id,
                    percent: userData.percent,
                    comment: userData.comment,
                    param_config: userData.param_config,
                    final_royalty: userData.final_royalty ?? 0,
                    suggest_royalty: userData.suggest_royalty ?? 0,
                    file_id: userData.file_id,
                    created_by: userId,
                    created_at: new Date(),
                };

                await queryRunner.manager.insert(ArticleRoyaltyUserEntity, userToInsert);
            }
        }
    }

    /**
     * Update article_royalty_groups.publish_date for all groups of an article
     */
    private async updateRoyaltyGroupsPublishDate(
        queryRunner: QueryRunner,
        articleId: number,
        publishDate: Date
    ): Promise<void> {
        await queryRunner.manager.update(
            ArticleRoyaltyGroupEntity,
            { article_id: articleId },
            {
                publish_date: publishDate,
                updated_at: new Date(),
            }
        );
    }

    async saveArticle(
        data: ArticleSaveInputDto,
        userId: number,
        queryRunnerProps?: QueryRunner
    ): Promise<ArticleEntity> {
        const { article_kind_ids, ...articleData } = data;

        // Extract only the fields that are actually saved in the database
        const validFields = this.repo.metadata.columns.map((col) => col.propertyName);
        const filteredArticleData = Object.fromEntries(
            Object.entries(articleData).filter(([key]) => validFields.includes(key))
        );

        const queryRunner = queryRunnerProps || this.dataSource.createQueryRunner();
        const isRootTransaction = !queryRunnerProps;
        const startTime = Date.now();

        // Only start transaction if this is the root call
        if (isRootTransaction) {
            await queryRunner.connect();
            await queryRunner.startTransaction();
        }

        // Validate pseudonym before starting transaction operations
        if (!data.pseudonym_id && !data.pseudonym_name) {
            throw new BadRequestException('Pseudonym id or Pseudonym name is required');
        }

        // Batch validation queries to reduce round trips
        const validationPromises: Promise<any>[] = [];

        if (data.pseudonym_id) {
            validationPromises.push(
                queryRunner.manager.findOne(PseudonymsEntity, {
                    where: { id: data.pseudonym_id },
                })
            );
        }

        // Execute all validation queries in parallel
        const [pseudonymValidation] = await Promise.all(validationPromises);

        if (data.pseudonym_id && !pseudonymValidation) {
            throw new BadRequestException('Pseudonym not found');
        }

        let article: ArticleEntity | undefined;

        // Collect all changes for unified logging
        const logData: any = {
            article_data: {},
            changes: {},
            related_entities: {},
        };

        try {
            // Optimized pseudonym handling
            if (data.pseudonym_id) {
                filteredArticleData.pseudonym_id = data.pseudonym_id;
            } else if (data.pseudonym_name) {
                // Use upsert pattern to avoid race conditions
                const pseudonym = await queryRunner.manager.findOne(PseudonymsEntity, {
                    where: { name: data.pseudonym_name },
                });

                if (!pseudonym) {
                    const pseudonymInsertResult = await queryRunner.manager
                        .createQueryBuilder()
                        .insert()
                        .into(PseudonymsEntity)
                        .values({
                            name: data.pseudonym_name,
                            status_id: ItemStatus.ACTIVE,
                            is_default: false,
                            user_id: userId,
                        })
                        .orIgnore() // Handle race condition if another process creates the same pseudonym
                        .returning('id')
                        .execute();

                    if (pseudonymInsertResult.identifiers.length > 0) {
                        filteredArticleData.pseudonym_id = pseudonymInsertResult.identifiers[0].id;
                    } else {
                        // If insert was ignored, fetch the existing pseudonym
                        const existingPseudonym = await queryRunner.manager.findOneOrFail(PseudonymsEntity, {
                            where: { name: data.pseudonym_name },
                        });
                        filteredArticleData.pseudonym_id = existingPseudonym.id;
                    }
                } else {
                    filteredArticleData.pseudonym_id = pseudonym.id;
                }
            }

            // Optimized article creation or update
            let originalArticle: ArticleEntity | null = null;
            const workflow = await queryRunner.manager.findOne(WorkflowEntity, {
                where: {
                    department_id: data.department_id,
                    type_id: ItemWorkflowType.ARTICLE,
                    workflow_type_id: WorkflowType.PUBLISHED,
                },
            });
            if (data.id) {
                // Get original article data for change tracking
                originalArticle = await queryRunner.manager.findOne(ArticleEntity, {
                    where: { id: data.id },
                });

                // Update existing article
                filteredArticleData.updated_by = userId;
                filteredArticleData.updated_at = new Date();
                if (workflow?.id === filteredArticleData.workflow_id && !filteredArticleData.is_fixed_publish_date) {
                    filteredArticleData.publish_date = originalArticle?.publish_date ?? new Date();
                }

                await queryRunner.manager.update(ArticleEntity, data.id, filteredArticleData);
                article = await queryRunner.manager.findOneOrFail(ArticleEntity, {
                    where: { id: data.id },
                });

                // Update article_royalty_groups.publish_date when workflow matches
                if (workflow?.id === filteredArticleData.workflow_id && article.publish_date) {
                    await this.updateRoyaltyGroupsPublishDate(queryRunner, article.id, article.publish_date);
                }
            } else {
                // Create new article - avoid unnecessary fetch after insert
                const articleToInsert: Partial<ArticleEntity> = {
                    ...filteredArticleData,
                    created_by: userId,
                    created_at: new Date(),
                };
                if (workflow?.id === filteredArticleData.workflow_id && !filteredArticleData.publish_date) {
                    articleToInsert.publish_date = new Date();
                }
                console.log('🔍 Creating article with data:', articleToInsert);
                const insertResult = await queryRunner.manager.insert(ArticleEntity, articleToInsert);
                const articleId = insertResult.identifiers[0].id;
                console.log('✅ Article created with ID:', articleId);

                // Create article object without additional DB query
                article = Object.assign(new ArticleEntity(), {
                    ...articleToInsert,
                    id: articleId,
                });

                // Skip individual logging for creation - will log once at the end
            }

            // At this point, article is guaranteed to be defined
            if (!article) {
                throw new InternalServerErrorException('Failed to create or update article');
            }

            // Type assertion to help TypeScript understand article is now defined
            const definedArticle = article as ArticleEntity;

            await axios.post(appConf.ARTICLE_REVALIDATE_URL, {
                id: definedArticle.id,
                key: appConf.ARTICLE_REVALIDATE_TOKEN,
            });

            // Flag to track if this is a new article (for logging purposes)
            const isNewArticle = !data.id;

            // Collect article data for logging
            logData.article_data = {
                id: definedArticle.id,
                title: data.title,
                article_type_id: data.article_type_id,
                workflow_id: data.workflow_id,
                pseudonym_id: definedArticle.pseudonym_id,
                department_id: data.department_id,
            };

            // Collect field changes for unified logging (only for updates)
            if (!isNewArticle && originalArticle) {
                const fieldsToTrack = [
                    'title',
                    'desc',
                    'content',
                    // 'sub_title',
                    // 'brief_title',
                    // 'slug',
                    // 'workflow_id',
                    // 'article_type_id',
                    // 'pseudonym_id',
                    // 'department_id',
                    // 'publish_date',
                    // 'source',
                    // 'avatar1_id',
                    // 'avatar2_id',
                    // 'file_id',
                    // 'web_layout_id',
                    // 'mobile_layout_id',
                    // 'press_publication_id',
                    // 'issue_id',
                    // 'typesetting_status_id',
                    // 'is_sync',
                    // 'is_unclassified',
                ];

                const fieldChanges: any = {};
                for (const field of fieldsToTrack) {
                    const oldValue = originalArticle[field];
                    const newValue = definedArticle[field];

                        fieldChanges[field] = {
                            from: oldValue,
                            to: newValue,
                        };
                }

                if (Object.keys(fieldChanges).length > 0) {
                    logData.changes.field_changes = fieldChanges;
                }
            }

            // Batch delete operations for better performance
            const deletePromises: Promise<any>[] = [];

            if (data.related_article_ids && data.related_article_ids.length > 0) {
                deletePromises.push(
                    queryRunner.manager.delete(RelatedArticleEntity, {
                        article_id: definedArticle.id,
                    })
                );
            }

            if (article_kind_ids && article_kind_ids.length > 0) {
                deletePromises.push(
                    queryRunner.manager.delete(ArticleArticleKindEntity, {
                        article_id: definedArticle.id,
                    })
                );
            }

            // Execute all deletes in parallel
            if (deletePromises.length > 0) {
                await Promise.all(deletePromises);
            }

            // Batch insert operations
            const insertPromises: Promise<any>[] = [];

            if (data.related_article_ids && data.related_article_ids.length > 0) {
                const relatedArticles = data.related_article_ids.map((relatedArticleId) => ({
                    article_id: definedArticle.id,
                    related_article_id: relatedArticleId,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(RelatedArticleEntity, relatedArticles));

                // Collect related articles data for unified logging
                logData.related_entities.related_articles = {
                    ids: data.related_article_ids,
                    count: data.related_article_ids.length,
                };
            }

            if (article_kind_ids && article_kind_ids.length > 0) {
                const articleKinds = article_kind_ids.map((kindId) => ({
                    article_id: definedArticle.id,
                    article_kind_id: kindId,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(ArticleArticleKindEntity, articleKinds));

                // Collect article kinds data for unified logging
                logData.related_entities.article_kinds = {
                    ids: article_kind_ids,
                    count: article_kind_ids.length,
                };
            }

            // Optimized tags handling
            if (data.article_tags && data.article_tags.length > 0) {
                // Add tag deletion to the batch delete operations
                insertPromises.push(
                    queryRunner.manager.delete(ArticleTagEntity, {
                        article_id: definedArticle.id,
                    })
                );

                // Find existing tags in one query
                const existingTags = await queryRunner.manager.find(TagEntity, {
                    where: {
                        name: In(data.article_tags),
                        department_id: data.department_id,
                    },
                });

                const existingTagNames = new Set(existingTags.map((tag) => tag.name));
                const tagsToCreate = data.article_tags.filter((tagName) => !existingTagNames.has(tagName));

                // Bulk create new tags if needed
                let newTags: TagEntity[] = [];
                if (tagsToCreate.length > 0) {
                    const tagEntities = tagsToCreate.map((tag) => ({
                        name: tag,
                        department_id: data.department_id,
                        slug: slugify(tag, { lower: true }),
                        status_id: ItemStatus.ACTIVE,
                        created_by: userId,
                        created_at: new Date(),
                    }));

                    const tagInsertResult = await queryRunner.manager.insert(TagEntity, tagEntities);

                    // Create tag objects from insert result
                    newTags = tagInsertResult.identifiers.map(
                        (identifier, index) =>
                            ({
                                id: identifier.id,
                                ...tagEntities[index],
                            }) as TagEntity
                    );
                }

                // Combine all tags and create article-tag relationships
                const allTags = [...existingTags, ...newTags];
                const articleTags = allTags.map((tag) => ({
                    article_id: definedArticle.id,
                    tag_id: tag.id,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(ArticleTagEntity, articleTags));

                // Collect tags data for unified logging
                logData.related_entities.tags = {
                    tags: data.article_tags,
                    new_tags_created: tagsToCreate,
                    existing_tags_used: existingTagNames.size,
                    count: data.article_tags.length,
                };
            }

            // Add notes to batch insert operations
            if (data.new_article_notes && data.new_article_notes.length > 0) {
                const newNotes = data.new_article_notes.map((note) => ({
                    article_id: definedArticle.id,
                    content: note,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(ArticleNoteEntity, newNotes));

                // Collect notes data for unified logging
                logData.related_entities.notes = {
                    notes: data.new_article_notes,
                    count: data.new_article_notes.length,
                };
            }

            // Execute all insert operations in parallel
            if (insertPromises.length > 0) {
                await Promise.all(insertPromises);
            }

            // Optimized categories handling
            if (data.article_categories && data.article_categories.length > 0) {
                // Delete existing categories for this article
                await queryRunner.manager.delete(ArticleCategoryEntity, { article_id: definedArticle.id });

                // Create new categories with is_major field
                const categories = data.article_categories.map((cat) => ({
                    article_id: definedArticle.id,
                    category_id: cat.category_id,
                    display_order: cat.display_order,
                    is_major: cat.is_major, // Make sure is_major is included
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleCategoryEntity, categories);

                // Collect categories data for unified logging
                logData.related_entities.categories = {
                    categories: data.article_categories.map((cat) => ({
                        category_id: cat.category_id,
                        display_order: cat.display_order,
                        is_major: cat.is_major,
                    })),
                    count: data.article_categories.length,
                    workflow_type: 'regular',
                };
            }

            // Parallel execution of independent operations
            const parallelOperations: Promise<any>[] = [];

            // Handle article files from content
            parallelOperations.push(
                this.handleArticleFiles(
                    definedArticle.id,
                    data.content,
                    userId,
                    queryRunner,
                    !data.id // isNewArticle = true if no id provided
                )
            );

            // Handle article issue pages
            if (data.article_issue_pages && data.article_issue_pages.length > 0) {
                const issuePageData = data.article_issue_pages; // Capture in local variable for type safety

                const issuePageOperation = async () => {
                    await queryRunner.manager.delete(ArticleIssuePageEntity, {
                        article_id: definedArticle.id,
                    });

                    const articleIssuePages = issuePageData.map((articleIssuePage) => ({
                        article_id: definedArticle.id,
                        issue_page_id: articleIssuePage.issue_page_id,
                        display_order: articleIssuePage.display_order,
                        comment: articleIssuePage.comment,
                        position: articleIssuePage.position,
                        created_by: userId,
                        created_at: new Date(),
                    }));

                    await queryRunner.manager.insert(ArticleIssuePageEntity, articleIssuePages);
                };

                parallelOperations.push(issuePageOperation());

                // Collect issue pages data for unified logging
                logData.related_entities.issue_pages = {
                    issue_pages: issuePageData.map((page) => ({
                        issue_page_id: page.issue_page_id,
                        display_order: page.display_order,
                        comment: page.comment,
                        position: page.position,
                    })),
                    count: issuePageData.length,
                };
            }

            // Execute parallel operations
            if (parallelOperations.length > 0) {
                await Promise.all(parallelOperations);
            }

            // Handle article royalty group (new nested approach)
            if (data.article_royalty_group) {
                await this.handleArticleRoyaltyGroup(
                    definedArticle.id,
                    data.article_royalty_group,
                    userId,
                    queryRunner
                );

                // Collect royalty group data for unified logging
                logData.related_entities.royalty_group = {
                    group_title: data.article_royalty_group.title,
                    workflow_id: data.article_royalty_group.workflow_id,
                    royalties_count: data.article_royalty_group.article_royalties?.length || 0,
                    total_users:
                        data.article_royalty_group.article_royalties?.reduce(
                            (sum, r) => sum + (r.article_royalty_users?.length || 0),
                            0
                        ) || 0,
                };
            }
            // Handle article royalties (legacy approach - for backward compatibility)
            else if (data.article_royalties && data.article_royalties.length > 0) {
                await this.handleArticleRoyalties(definedArticle.id, data.article_royalties, userId, queryRunner);

                // Collect royalties data for unified logging
                logData.related_entities.royalties = {
                    royalties: data.article_royalties.map((royalty) => ({
                        type_id: royalty.type_id,
                        royalty_type_id: royalty.royalty_type_id,
                        suggest_royalty: royalty.suggest_royalty,
                        users_count: royalty.article_royalty_users?.length || 0,
                    })),
                    total_royalties: data.article_royalties.length,
                    total_users: data.article_royalties.reduce(
                        (sum, r) => sum + (r.article_royalty_users?.length || 0),
                        0
                    ),
                };
            }

            // Handle article type copies - keep sequential to avoid conflicts
            if (data.article_type_copy_ids && data.article_type_copy_ids.length > 0) {
                const { article_type_copy_ids, is_sync, article_categories, ...baseData } = data;

                // Create copy articles sequentially to avoid conflicts
                const createdCopyIds: number[] = [];
                for (const articleTypeId of article_type_copy_ids) {
                    const copyArticle = await this.saveArticle(
                        {
                            ...baseData,
                            article_type_id: articleTypeId,
                            article_type_copy_ids: undefined,
                            root_article_id: definedArticle.id,
                            article_categories: [],
                        },
                        userId,
                        queryRunner
                    );
                    createdCopyIds.push(copyArticle.id);
                }

                // Collect article copies data for unified logging
                logData.related_entities.article_copies = {
                    copy_article_type_ids: article_type_copy_ids,
                    created_copy_article_ids: createdCopyIds,
                    count: article_type_copy_ids.length,
                };
            }

            // Final update for is_unclassified flag
            const isUnclassified = !!data.is_unclassified && (data.article_type_copy_ids ?? []).length === 0;
            if (definedArticle.is_unclassified !== isUnclassified) {
                await queryRunner.manager.update(ArticleEntity, definedArticle.id, {
                    is_unclassified: isUnclassified,
                });
                definedArticle.is_unclassified = isUnclassified;
            }

            // Ensure all related entities are represented in log data (even if empty)
            if (!logData.related_entities.related_articles && data.related_article_ids?.length) {
                logData.related_entities.related_articles = {
                    ids: data.related_article_ids,
                    count: data.related_article_ids.length,
                };
            }
            if (!logData.related_entities.article_kinds && article_kind_ids?.length) {
                logData.related_entities.article_kinds = { ids: article_kind_ids, count: article_kind_ids.length };
            }
            if (!logData.related_entities.tags && data.article_tags?.length) {
                logData.related_entities.tags = { tags: data.article_tags, count: data.article_tags.length };
            }
            if (!logData.related_entities.notes && data.new_article_notes?.length) {
                logData.related_entities.notes = {
                    notes: data.new_article_notes,
                    count: data.new_article_notes.length,
                };
            }
            if (!logData.related_entities.categories && data.article_categories?.length) {
                logData.related_entities.categories = {
                    categories: data.article_categories.map((cat) => ({
                        category_id: cat.category_id,
                        display_order: cat.display_order,
                        is_major: cat.is_major,
                    })),
                    count: data.article_categories.length,
                    workflow_type: 'regular',
                };
            }
            // Handle logging for legacy royalties approach
            if (
                !logData.related_entities.royalties &&
                !logData.related_entities.royalty_group &&
                data.article_royalties?.length
            ) {
                logData.related_entities.royalties = {
                    royalties: data.article_royalties.map((royalty) => ({
                        type_id: royalty.type_id,
                        royalty_type_id: royalty.royalty_type_id,
                        suggest_royalty: royalty.suggest_royalty,
                        users_count: royalty.article_royalty_users?.length || 0,
                    })),
                    total_royalties: data.article_royalties.length,
                    total_users: data.article_royalties.reduce(
                        (sum, r) => sum + (r.article_royalty_users?.length || 0),
                        0
                    ),
                };
            }

            // Create unified log entry - only one record per saveArticle call
            const action = isNewArticle ? 'ARTICLE_CREATED' : 'ARTICLE_UPDATED';
            const totalRelatedEntities =
                (data.related_article_ids?.length || 0) +
                (article_kind_ids?.length || 0) +
                (data.article_tags?.length || 0) +
                (data.new_article_notes?.length || 0) +
                (data.article_categories?.length || 0) +
                (data.article_royalties?.length || 0) +
                (data.article_royalty_group?.article_royalties?.length || 0) +
                (data.article_type_copy_ids?.length || 0) +
                (data.article_issue_pages?.length || 0);

            // Extract workflow IDs for comprehensive logging (in case of manual save operations)
            const oldWorkflowId = originalArticle?.workflow_id;
            const newWorkflowId = data.workflow_id;

            await this.createArticleLog(
                definedArticle.id,
                action,
                logData,
                userId,
                queryRunner,
                {
                    operation: isNewArticle ? 'create' : 'update',
                    success: true,
                    execution_time: Date.now() - startTime,
                    total_related_entities: totalRelatedEntities,
                    changed_fields: logData.changes.field_changes ? Object.keys(logData.changes.field_changes) : [],
                    changes_count: logData.changes.field_changes
                        ? Object.keys(logData.changes.field_changes).length
                        : 0,
                },
                oldWorkflowId,
                newWorkflowId
            );

            // Commit transaction only if this is the root call
            if (isRootTransaction) {
                console.log('🔍 Committing transaction for article:', definedArticle.id);
                await queryRunner.commitTransaction();
                console.log('✅ Transaction committed successfully');
            }

            return definedArticle;
        } catch (error) {
            console.error('🚨 Error in saveArticle:', error.message);
            console.error('🚨 Error stack:', error.stack);

            // Log error if we have an article ID
            if (article?.id) {
                try {
                    await this.createArticleLog(
                        article.id,
                        'ARTICLE_SAVE_ERROR',
                        {
                            error_message: error.message,
                            error_type: error.constructor.name,
                            operation_data: {
                                is_update: !!data.id,
                                has_related_data: !!(
                                    data.related_article_ids?.length ||
                                    data.article_tags?.length ||
                                    data.article_categories?.length ||
                                    data.article_royalties?.length ||
                                    data.article_royalty_group
                                ),
                            },
                        },
                        userId,
                        queryRunner,
                        {
                            operation: 'error',
                            success: false,
                            execution_time: Date.now() - startTime,
                        },
                        undefined, // no workflow IDs for error logs
                        undefined
                    );
                } catch (logError) {
                    // Don't let logging errors interfere with the main error
                    console.error('Failed to log article save error:', logError);
                }
            }

            // Rollback transaction only if this is the root call
            if (isRootTransaction) {
                await queryRunner.rollbackTransaction();
            }

            // Re-throw business logic exceptions as-is
            if (
                error instanceof BadRequestException ||
                error instanceof NotFoundException ||
                error instanceof ForbiddenException ||
                error instanceof BusinessException
            ) {
                throw error;
            }

            // Wrap other errors as internal server errors with more context
            throw new InternalServerErrorException(
                `An error occurred while saving the article${data.id ? ` (ID: ${data.id})` : ' (new article)'}`,
                { cause: error }
            );
        } finally {
            // Release connection only if this is the root call
            if (isRootTransaction) {
                await queryRunner.release();
            }
        }
    }

    async changeLock(id: number, userId: number, lock: boolean) {
        const article = await this.findOne(id);
        if (!article) {
            throw new NotFoundException();
        }
        if (!article.lock_user_id && !lock) {
            throw new ForbiddenException();
        }
        await this.repo.update(
            { id },
            {
                lock_user_id: lock ? userId : null,
                lock_at: lock ? new Date() : null,
            }
        );
    }

    async cloneArticle(id: number, body: ArticleCloneInputDto, userId: number) {
        const article = await this.findOne({ where: { id }, relations: ['childrenArticles'] });
        if (!article) {
            throw new NotFoundException();
        }
        if (article.article_type_id === body.article_type_id) {
            throw new BadRequestException('Cannot clone to the same article type');
        }
        if (article.childrenArticles?.some((child) => child.article_type_id === body.article_type_id)) {
            throw new BadRequestException('Cannot clone when children articles are cloned to the same article type');
        }
        return this.create({
            ...article,
            ...body,
            id: undefined,
            created_by: userId,
            updated_by: undefined,
            updated_at: undefined,
        });
    }
}
