import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { CategoryEntity } from '../../entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CategorySaveInputDto } from './dtos/category-save-input.dto';

@Injectable()
export class CategoriesService extends BaseService<CategoryEntity> {
    constructor(@InjectRepository(CategoryEntity) private readonly repo: Repository<CategoryEntity>) {
        super(repo);
    }

    async updateCategory(body: CategorySaveInputDto, userId: number): Promise<CategoryEntity> {
        if (!body.is_major) {
            const parent = await this.findOne(body.id!);
            if (!parent) throw new NotFoundException();
            return this.repo.manager.transaction(async (manager) => {
                await manager.update(CategoryEntity, { parent_id: body.id }, { is_major: false, updated_by: userId });
                return this.save({ ...body, updated_by: userId });
            });
        } else return this.updateOne(body.id!, { ...body, updated_by: userId });
    }
}
