import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { FileEntity } from '../../../entities/file.entity';
import { ItemStatus } from '../../../commons/enums.common';

@InputType()
@AutoSanitize()
export class IssuePageFileCommentUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(FileEntity)
    file_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;
}
