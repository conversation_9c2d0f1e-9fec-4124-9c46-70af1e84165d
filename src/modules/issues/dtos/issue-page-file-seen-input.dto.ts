import { Field, InputType, Int } from '@nestjs/graphql';
import { IsBoolean, IsNotEmpty } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
@AutoSanitize()
export class IssuePageFileSeenInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(FileEntity)
    file_id: number;

    @Field(() => Boolean)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsBoolean()
    is_seen: boolean;
}
