import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsISO8601, IsNotEmpty, IsOptional, IsPositive, IsString, Min } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FileEntity } from '../../../entities/file.entity';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { IssueEntity } from '../../../entities/issue.entity';

@InputType()
@AutoSanitize()
export class IssueSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(IssueEntity)
    name: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    file_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(PressPublicationEntity)
    press_publication_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    all_count: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsISO8601()
    publish_date?: string;
}
