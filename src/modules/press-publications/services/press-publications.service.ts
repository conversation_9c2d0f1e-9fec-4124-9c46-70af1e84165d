import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { PressPublicationSaveInputDto } from '../dtos/press-publication-save-input.dto';
import { IssueEntity } from '../../../entities/issue.entity';
import { ItemStatus } from '../../../commons/enums.common';

@Injectable()
export class PressPublicationsService extends BaseService<PressPublicationEntity> {
    constructor(@InjectRepository(PressPublicationEntity) public readonly repo: Repository<PressPublicationEntity>) {
        super(repo);
    }

    async update(id: number, data: PressPublicationSaveInputDto, userId: number): Promise<PressPublicationEntity> {
        if (data.issue_pre_created > 0 && data.status_id === ItemStatus.ACTIVE && data.issue_status_id === ItemStatus.ACTIVE) {
            const countIssue = await this.repo.manager.count(IssueEntity, {
                where: { press_publication_id: id, publish_date: MoreThan(new Date()) },
            });
            if (countIssue < data.issue_pre_created) {
                const pressPublication = await this.findOne(id);
                if (!pressPublication) throw new NotFoundException();
                const issueLasted = await this.repo.manager.findOne(IssueEntity, {
                    where: { press_publication_id: id },
                    order: { publish_date: 'DESC' },
                });
                if (!issueLasted) return this.updateOne(id, data);
                let yearCount = issueLasted.year_count;
                let allCount = issueLasted.all_count;
                const issues = Array.from({ length: data.issue_pre_created - countIssue }, () => {
                    return this.repo.manager.create(IssueEntity, {
                        name: `${pressPublication.issue_title} ${issueLasted.all_count}`,
                        status_id: ItemStatus.PENDING,
                        press_publication_id: id,
                        department_id: issueLasted.department_id,
                        year_count: yearCount++,
                        all_count: allCount++,
                        created_by: userId,
                        //publish_date: '',//TODO:
                    });
                });
                return this.repo.manager.transaction(async (manager) => {
                    await manager.save(issues);
                    const cleanedData = Object.fromEntries(
                        Object.entries(data).filter(([_, value]) => value !== undefined)
                    );
                    Object.assign(pressPublication, cleanedData);
                    return manager.save(pressPublication);
                });
            } else return this.updateOne(id, data);
        } else return this.updateOne(id, data);
    }
}
