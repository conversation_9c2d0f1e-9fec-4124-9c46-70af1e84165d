import { Field, Int, ObjectType } from '@nestjs/graphql';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { RoyaltyFormulaEntity } from '../../../entities/royalty-formula.entity';

@ObjectType()
export class RoyaltyFormulasModel implements IPaginatedType<RoyaltyFormulaEntity> {
    @Field(() => [RoyaltyFormulaEntity])
    data: RoyaltyFormulaEntity[];

    @Field(() => Int)
    totalCount: number;

    @Field(() => Int)
    totalPages: number;

    @Field(() => Int)
    currentPage: number;
}
