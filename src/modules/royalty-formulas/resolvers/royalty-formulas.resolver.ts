import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { RoyaltyFormulaEntity } from '../../../entities/royalty-formula.entity';
import { RoyaltyFormulasService } from '../services/royalty-formulas.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { RoyaltyFormulasModel } from '../models/royalty-formulas.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { RoyaltyFormulaSaveInputDto } from '../dtos/royalty-formula-save-input.dto';
import { ItemStatus } from '../../../commons/enums.common';
import { DepartmentEntity } from '../../../entities/department.entity';

@AuthResolver(RoyaltyFormulaEntity)
export class RoyaltyFormulasResolver {
    constructor(
        private readonly royaltyFormulasService: RoyaltyFormulasService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async created_by(@Parent() parent: RoyaltyFormulaEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updated_by(@Parent() parent: RoyaltyFormulaEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async deleted_by(@Parent() parent: RoyaltyFormulaEntity): Promise<UserEntity | null> {
        if (!parent.deleted_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.deleted_by);
    }

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: RoyaltyFormulaEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @Query(() => RoyaltyFormulasModel, { name: 'royalty_formulas_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<RoyaltyFormulaEntity>> {
        return this.royaltyFormulasService.search(body);
    }

    @Query(() => RoyaltyFormulaEntity, { name: 'royalty_formulas_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<RoyaltyFormulaEntity> {
        return this.royaltyFormulasService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => RoyaltyFormulaEntity, { name: 'royalty_formulas_create' })
    async store(
        @Args('body') body: RoyaltyFormulaSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyFormulaEntity> {
        return this.royaltyFormulasService.create({
            ...body,
            created_by: auth.id,
            status_id: body.status_id ?? ItemStatus.ACTIVE,
            order: body.order ?? 0,
        });
    }

    @Mutation(() => RoyaltyFormulaEntity, { name: 'royalty_formulas_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: RoyaltyFormulaSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyFormulaEntity> {
        return this.royaltyFormulasService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'royalty_formulas_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.royaltyFormulasService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => [RoyaltyFormulaEntity], { name: 'royalty_formulas_bulk_update' })
    async bulkUpdate(
        @Args('body', { type: () => [RoyaltyFormulaSaveInputDto] }) body: RoyaltyFormulaSaveInputDto[],
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyFormulaEntity[]> {
        const results: RoyaltyFormulaEntity[] = [];

        for (const item of body) {
            if (item.id) {
                // Update existing
                const updated = await this.royaltyFormulasService.updateOne(item.id, {
                    ...item,
                    updated_by: auth.id,
                });
                results.push(updated);
            } else {
                // Create new
                const created = await this.royaltyFormulasService.create({
                    ...item,
                    created_by: auth.id,
                    status_id: item.status_id ?? ItemStatus.ACTIVE,
                    order: item.order ?? 0,
                });
                results.push(created);
            }
        }

        return results;
    }
}
