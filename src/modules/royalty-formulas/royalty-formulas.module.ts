import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoyaltyFormulaEntity } from '../../entities/royalty-formula.entity';
import { RoyaltyFormulasService } from './services/royalty-formulas.service';
import { RoyaltyFormulasResolver } from './resolvers/royalty-formulas.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([RoyaltyFormulaEntity])],
    providers: [RoyaltyFormulasService, RoyaltyFormulasResolver],
})
export class RoyaltyFormulasModule {}
