import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { RoyaltyFormulaEntity } from '../../../entities/royalty-formula.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class RoyaltyFormulasService extends BaseService<RoyaltyFormulaEntity> {
    constructor(@InjectRepository(RoyaltyFormulaEntity) public readonly repo: Repository<RoyaltyFormulaEntity>) {
        super(repo);
    }
}
