import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { UserEntity } from '../../../entities/user.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { TelevisionProgramEntity } from '../../../entities/television-program.entity';
import { TelevisionWorkflowEntity } from '../../../entities/television-workflow.entity';
import { TelevisionProgramsService } from '../services/television-programs.service';
import { TelevisionProgramSaveInputDto } from '../dtos/television-program-save-input.dto';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { TelevisionProgramsModel } from '../models/television-programs.model';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AuthUser } from '../../auth/auth.decorator';

@AuthResolver(TelevisionProgramEntity)
export class TelevisionProgramsResolver {
    constructor(
        private readonly televisionProgramsService: TelevisionProgramsService,
        private readonly dataLoader: DataLoaderService
    ) {
    }

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: TelevisionProgramEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [TelevisionWorkflowEntity], { nullable: true })
    async televisionWorkflows(@Parent() parent: TelevisionProgramEntity): Promise<TelevisionWorkflowEntity[]> {
        // Use a custom query since this is a many-to-many relationship through junction table
        return await this.televisionProgramsService.repo.manager
            .createQueryBuilder(TelevisionWorkflowEntity, 'workflow')
            .innerJoin('television_program_workflows', 'junction', 'junction.television_workflow_id = workflow.id')
            .where('junction.television_program_id = :programId', { programId: parent.id })
            .getMany();
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: TelevisionProgramEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: TelevisionProgramEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @Query(() => TelevisionProgramsModel, { name: 'television_programs_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<TelevisionProgramEntity>> {
        return this.televisionProgramsService.search(body);
    }

    @Query(() => TelevisionProgramEntity, { name: 'television_programs_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<TelevisionProgramEntity> {
        return this.televisionProgramsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => TelevisionProgramEntity, { name: 'television_programs_create' })
    async store(@Args('body') body: TelevisionProgramSaveInputDto, @AuthUser() auth: UserEntity): Promise<TelevisionProgramEntity> {
        return this.televisionProgramsService.createWithWorkflows(body, auth.id);
    }

    @Mutation(() => TelevisionProgramEntity, { name: 'television_programs_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TelevisionProgramSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TelevisionProgramEntity> {
        return this.televisionProgramsService.updateWithWorkflows(id, body, auth.id);
    }

    @Mutation(() => Boolean, { name: 'television_programs_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.televisionProgramsService.softDelete(id, auth.id);
        return true;
    }
}