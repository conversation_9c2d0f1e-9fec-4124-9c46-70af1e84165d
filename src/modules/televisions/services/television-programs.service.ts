import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { TelevisionProgramEntity } from '../../../entities/television-program.entity';
import { TelevisionProgramSaveInputDto } from '../dtos/television-program-save-input.dto';

@Injectable()
export class TelevisionProgramsService extends BaseService<TelevisionProgramEntity> {
    constructor(
        @InjectRepository(TelevisionProgramEntity)
        readonly repo: Repository<TelevisionProgramEntity>,
    ) {
        super(repo);
    }

    /**
     * Create television program with workflows
     */
    async createWithWorkflows(
        data: TelevisionProgramSaveInputDto,
        userId: number
    ): Promise<TelevisionProgramEntity> {
        return this.repo.manager.transaction(async (manager) => {
            // Create the program
            const program = manager.create(TelevisionProgramEntity, {
                name: data.name,
                desc: data.desc,
                status_id: data.status_id,
                department_id: data.department_id,
                created_by: userId,
                created_at: new Date()
            });

            const savedProgram = await manager.save(program);

            // Handle workflow relationships
            if (data.television_workflow_ids && data.television_workflow_ids.length > 0) {
                await this.updateWorkflowRelationships(
                    manager,
                    savedProgram.id,
                    data.television_workflow_ids
                );
            }

            return savedProgram;
        });
    }

    /**
     * Update television program with workflows
     */
    async updateWithWorkflows(
        id: number,
        data: TelevisionProgramSaveInputDto,
        userId: number
    ): Promise<TelevisionProgramEntity> {
        return this.repo.manager.transaction(async (manager) => {
            // Update the program
            await manager.update(TelevisionProgramEntity, id, {
                name: data.name,
                desc: data.desc,
                status_id: data.status_id,
                department_id: data.department_id,
                updated_by: userId,
                updated_at: new Date()
            });

            // Handle workflow relationships
            if (data.television_workflow_ids !== undefined) {
                await this.updateWorkflowRelationships(
                    manager,
                    id,
                    data.television_workflow_ids
                );
            }

            return manager.findOneOrFail(TelevisionProgramEntity, { where: { id } });
        });
    }

    /**
     * Update workflow relationships for a program
     */
    private async updateWorkflowRelationships(
        manager: any,
        programId: number,
        workflowIds: number[]
    ): Promise<void> {
        // Clear existing relationships
        await manager.query(
            `DELETE FROM television_program_workflows WHERE television_program_id = $1`,
            [programId]
        );

        // Add new relationships
        if (workflowIds.length > 0) {
            const values = workflowIds.map(workflowId => 
                `(${programId}, ${workflowId})`
            ).join(', ');

            await manager.query(
                `INSERT INTO television_program_workflows (television_program_id, television_workflow_id) VALUES ${values}`
            );
        }
    }
}