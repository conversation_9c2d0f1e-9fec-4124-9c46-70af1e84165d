import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { TelevisionProgramEntity } from '../../../entities/television-program.entity';

@Injectable()
export class TelevisionProgramsService extends BaseService<TelevisionProgramEntity> {
    constructor(
        @InjectRepository(TelevisionProgramEntity)
        protected readonly repo: Repository<TelevisionProgramEntity>
    ) {
        super(repo);
    }
}