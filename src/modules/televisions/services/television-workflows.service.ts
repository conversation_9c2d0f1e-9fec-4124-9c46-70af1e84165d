import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { TelevisionWorkflowEntity } from '../../../entities/television-workflow.entity';
import { TelevisionWorkflowUserEntity } from '../../../entities/television-workflow-user.entity';
import { TelevisionWorkflowSaveInputDto, TelevisionWorkflowUserInput } from '../dtos/television-workflow-save-input.dto';

@Injectable()
export class TelevisionWorkflowsService extends BaseService<TelevisionWorkflowEntity> {
    constructor(
        @InjectRepository(TelevisionWorkflowEntity)
        protected readonly repo: Repository<TelevisionWorkflowEntity>,
    ) {
        super(repo);
    }

    /**
     * Create television workflow with users
     */
    async createWithUsers(
        data: TelevisionWorkflowSaveInputDto,
        userId: number
    ): Promise<TelevisionWorkflowEntity> {
        return this.repo.manager.transaction(async (manager) => {
            // Create the workflow
            const workflow = manager.create(TelevisionWorkflowEntity, {
                name: data.name,
                desc: data.desc,
                status_id: data.status_id,
                department_id: data.department_id,
                created_by: userId,
                created_at: new Date()
            });

            const savedWorkflow = await manager.save(workflow);

            // Handle workflow users
            if (data.television_workflow_users && data.television_workflow_users.length > 0) {
                await this.handleWorkflowUsers(
                    manager,
                    savedWorkflow.id,
                    data.television_workflow_users,
                    userId
                );
            }

            return savedWorkflow;
        });
    }

    /**
     * Update television workflow with users
     */
    async updateWithUsers(
        id: number,
        data: TelevisionWorkflowSaveInputDto,
        userId: number
    ): Promise<TelevisionWorkflowEntity> {
        return this.repo.manager.transaction(async (manager) => {
            // Update the workflow
            await manager.update(TelevisionWorkflowEntity, id, {
                name: data.name,
                desc: data.desc,
                status_id: data.status_id,
                department_id: data.department_id,
                updated_by: userId,
                updated_at: new Date()
            });

            // Handle workflow users
            if (data.television_workflow_users !== undefined) {
                await this.handleWorkflowUsers(
                    manager,
                    id,
                    data.television_workflow_users,
                    userId
                );
            }

            return manager.findOneOrFail(TelevisionWorkflowEntity, { where: { id } });
        });
    }

    /**
     * Handle workflow users for a specific workflow
     */
    private async handleWorkflowUsers(
        manager: any,
        workflowId: number,
        usersData: TelevisionWorkflowUserInput[],
        userId: number
    ): Promise<void> {
        // Get existing workflow users
        const existingUsers = await manager.find(TelevisionWorkflowUserEntity, {
            where: { television_workflow_id: workflowId, deleted_at: IsNull() },
        });
        const inputUserIds = usersData.filter(u => u.id).map(u => u.id);

        // Find users to soft delete (exist in DB but not in input)
        const usersToDelete = existingUsers.filter(u => !inputUserIds.includes(u.id));

        // Soft delete removed users
        if (usersToDelete.length > 0) {
            await manager.update(
                TelevisionWorkflowUserEntity,
                { id: In(usersToDelete.map(u => u.id)) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );
        }

        // Process each user in input
        for (const userData of usersData) {
            if (userData.id) {
                // Update existing user
                const existingUser = existingUsers.find(u => u.id === userData.id);
                if (existingUser) {
                    await manager.update(TelevisionWorkflowUserEntity, userData.id, {
                        user_id: userData.user_id,
                        type_id: userData.type_id,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                }
            } else {
                // Create new user
                const newUser = manager.create(TelevisionWorkflowUserEntity, {
                    television_workflow_id: workflowId,
                    user_id: userData.user_id,
                    type_id: userData.type_id,
                    created_by: userId,
                    created_at: new Date(),
                });

                await manager.save(newUser);
            }
        }
    }
}