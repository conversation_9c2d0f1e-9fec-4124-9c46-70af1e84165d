import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TelevisionProgramEntity } from '../../entities/television-program.entity';
import { TelevisionWorkflowEntity } from '../../entities/television-workflow.entity';
import { TelevisionProgramsService } from './services/television-programs.service';
import { TelevisionProgramsResolver } from './resolvers/television-programs.resolver';
import { TelevisionWorkflowsService } from './services/television-workflows.service';
import { TelevisionWorkflowsResolver } from './resolvers/television-workflows.resolver';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            TelevisionProgramEntity,
            TelevisionWorkflowEntity,
        ]),
    ],
    providers: [
        TelevisionWorkflowsService,
        TelevisionWorkflowsResolver,
        TelevisionProgramsService,
        TelevisionProgramsResolver,
    ],
    exports: [
        TelevisionProgramsService,
    ],
})
export class TelevisionsModule {}