import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TelevisionProgramEntity } from '../../entities/television-program.entity';
import { TelevisionProgramsService } from './services/television-programs.service';
import { TelevisionProgramsResolver } from './resolvers/television-programs.resolver';
import { TelevisionWorkflowsResolver } from './resolvers/television-workflows.resolver';
import { TelevisionWorkflowsService } from './services/television-workflows.service';
import { TelevisionWorkflowEntity } from '../../entities/television-workflow.entity';
import { TelevisionWorkflowUserEntity } from '../../entities/television-workflow-user.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            TelevisionProgramEntity,
            TelevisionWorkflowEntity,
            TelevisionWorkflowUserEntity,
        ]),
    ],
    providers: [
        TelevisionProgramsService,
        TelevisionProgramsResolver,
        TelevisionWorkflowsService,
        TelevisionWorkflowsResolver,
    ],
})
export class TelevisionsModule {}