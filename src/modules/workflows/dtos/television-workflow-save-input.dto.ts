import { Field, InputType, Int } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ItemStatus } from '../../../commons/enums.common';
import { DepartmentEntity } from '../../../entities/department.entity';
import { UserEntity } from '../../../entities/user.entity';
import { RoyaltyType } from '../../../entities/article-royalty.entity';

@InputType()
export class TelevisionWorkflowUserInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IdExists(UserEntity)
    user_id: number;

    @Field(() => Int)
    @IsEnum(RoyaltyType)
    type_id: RoyaltyType;
}

@InputType()
@AutoSanitize()
export class TelevisionWorkflowSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => [TelevisionWorkflowUserInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => TelevisionWorkflowUserInput)
    television_workflow_users?: TelevisionWorkflowUserInput[];
}