import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { UserEntity } from '../../../entities/user.entity';
import { TelevisionWorkflowEntity } from '../../../entities/television-workflow.entity';
import { TelevisionWorkflowUserEntity } from '../../../entities/television-workflow-user.entity';
import { TelevisionWorkflowsService } from '../services/television-workflows.service';
import { TelevisionWorkflowSaveInputDto } from '../dtos/television-workflow-save-input.dto';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { TelevisionWorkflowsModel } from '../models/television-workflows.model';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AuthUser } from '../../auth/auth.decorator';

@AuthResolver(() => TelevisionWorkflowEntity)
export class TelevisionWorkflowsResolver {
    constructor(
        private readonly televisionWorkflowsService: TelevisionWorkflowsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => [TelevisionWorkflowUserEntity], { nullable: true })
    async televisionWorkflowUsers(@Parent() parent: TelevisionWorkflowEntity): Promise<TelevisionWorkflowUserEntity[]> {
        return this.dataLoader.relationBatchOneMany(TelevisionWorkflowUserEntity, 'televisionWorkflow').load(parent.id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: TelevisionWorkflowEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: TelevisionWorkflowEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @Query(() => TelevisionWorkflowsModel, { name: 'television_workflows_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<TelevisionWorkflowEntity>> {
        return this.televisionWorkflowsService.search(body);
    }

    @Query(() => TelevisionWorkflowEntity, { name: 'television_workflows_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<TelevisionWorkflowEntity> {
        return this.televisionWorkflowsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => TelevisionWorkflowEntity, { name: 'television_workflows_create' })
    async store(@Args('body') body: TelevisionWorkflowSaveInputDto, @AuthUser() auth: UserEntity): Promise<TelevisionWorkflowEntity> {
        return this.televisionWorkflowsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => TelevisionWorkflowEntity, { name: 'television_workflows_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TelevisionWorkflowSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TelevisionWorkflowEntity> {
        return this.televisionWorkflowsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'television_workflows_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.televisionWorkflowsService.softDelete(id, auth.id);
        return true;
    }
}