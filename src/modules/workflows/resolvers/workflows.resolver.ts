import { WorkflowEntity } from '../../../entities/workflow.entity';
import { AuthMutation, AuthQuery, StaticAuthQuery } from '../../../commons/decorators/graphql.decorators';
import { WorkflowsService } from '../services/workflows.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { Args, Int, Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { DepartmentEntity } from '../../../entities/department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { WorkflowsModel } from '../models/workflows.model';
import { NotFoundException } from '@nestjs/common';
import { WorkflowSaveInputDto } from '../dtos/workflow-save-input.dto';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';

@Resolver(() => WorkflowEntity)
export class WorkflowsResolver {
    constructor(
        private readonly workflowsService: WorkflowsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: WorkflowEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @AuthQuery(() => WorkflowsModel, { name: 'workflows_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<WorkflowEntity>> {
        return this.workflowsService.search(body);
    }

    @AuthQuery(() => WorkflowEntity, { name: 'workflows_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<WorkflowEntity> {
        return this.workflowsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @AuthMutation(() => WorkflowEntity, { name: 'workflows_create' })
    async store(@Args('body') body: WorkflowSaveInputDto, @AuthUser() auth: UserEntity): Promise<WorkflowEntity> {
        return this.workflowsService.create({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => WorkflowEntity, { name: 'workflows_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: WorkflowSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<WorkflowEntity> {
        return this.workflowsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'workflows_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        return this.workflowsService.destroy(id, auth.id);
    }

    @StaticAuthQuery(() => Int, { name: 'workflows_id', nullable: true })
    async getWorkflowId(
        @Args('department_id', { type: () => Int }) department_id: number,
        @Args('workflow_type_id', { type: () => Int }) workflow_type_id: number,
        @Args('type_id', { type: () => Int }) type_id: number
    ): Promise<number | null> {
        return this.workflowsService
            .findOne({
                where: {
                    workflow_type_id: workflow_type_id,
                    department_id: department_id,
                    type_id: type_id,
                },
            })
            .then((res) => {
                return res?.id ?? null;
            });
    }
}
