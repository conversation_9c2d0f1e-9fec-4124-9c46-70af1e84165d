import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { TelevisionWorkflowEntity } from '../../../entities/television-workflow.entity';

@Injectable()
export class TelevisionWorkflowsService extends BaseService<TelevisionWorkflowEntity> {
    constructor(
        @InjectRepository(TelevisionWorkflowEntity)
        protected readonly repo: Repository<TelevisionWorkflowEntity>
    ) {
        super(repo);
    }
}