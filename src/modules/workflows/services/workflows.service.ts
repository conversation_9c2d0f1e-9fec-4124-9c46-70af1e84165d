import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { WorkflowEntity, WorkflowType } from '../../../entities/workflow.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ArticleEntity } from '../../../entities/article.entity';
import { ArticleRoyaltyGroupEntity } from '../../../entities/article-royalty-group.entity';

@Injectable()
export class WorkflowsService extends BaseService<WorkflowEntity> {
    constructor(@InjectRepository(WorkflowEntity) public readonly repo: Repository<WorkflowEntity>) {
        super(repo);
    }

    async destroy(id: number, deletedBy: number): Promise<Boolean> {
        const workflow = await this.findOne(id);
        if (!workflow) throw new NotFoundException();
        if (workflow.workflow_type_id !== WorkflowType.EDITING) throw new ForbiddenException();
        const [articleExists, articleRoyaltyExists] = await Promise.all([
            this.repo.manager.existsBy(ArticleEntity, { workflow_id: id }),
            this.repo.manager.existsBy(ArticleRoyaltyGroupEntity, { workflow_id: id }),
        ]);
        if (articleExists || articleRoyaltyExists) throw new ForbiddenException();
        await this.repo.update(id, {
            deleted_at: new Date(),
            deleted_by: deletedBy,
        });
        return true;
    }
}
