import { Module } from '@nestjs/common';
import { WorkflowsService } from './services/workflows.service';
import { WorkflowsResolver } from './resolvers/workflows.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkflowEntity } from '../../entities/workflow.entity';
import { WorkflowPermissionsService } from './services/workflow-permissions.service';
import { WorkflowPermissionArticleTypesService } from './services/workflow-permission-article-types.service';
import { WorkflowPermissionEntity } from '../../entities/workflow-permission.entity';
import { WorkflowPermissionArticleTypeEntity } from '../../entities/workflow-permission-article-type.entity';
import { WorkflowPermissionsResolver } from './resolvers/workflow-permissions.resolver';
import { WorkflowPermissionArticleTypesResolver } from './resolvers/workflow-permission-article-types.resolver';

@Module({
    providers: [
        WorkflowsService,
        WorkflowsResolver,
        WorkflowPermissionsService,
        WorkflowPermissionsResolver,
        WorkflowPermissionArticleTypesService,
        WorkflowPermissionArticleTypesResolver,
    ],
    imports: [
        TypeOrmModule.forFeature([
            WorkflowEntity,
            WorkflowPermissionEntity,
            WorkflowPermissionArticleTypeEntity,
        ]),
    ],
})
export class WorkflowsModule {}
