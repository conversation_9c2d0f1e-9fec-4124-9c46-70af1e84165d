import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LoggerService } from '../../loggers/logger.service';
import { MoreThan } from 'typeorm';
import { ItemStatus } from '../../commons/enums.common';
import { PressPublicationsService } from '../../modules/press-publications/services/press-publications.service';
import { IssueEntity } from '../../entities/issue.entity';

@Injectable()
export class AutoCreateIssuesService {
    constructor(
        private readonly loggerService: LoggerService,
        private readonly pressPublicationsService: PressPublicationsService
    ) {}

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async handleCron() {
        const pressPublications = await this.pressPublicationsService.find({
            where: { status_id: ItemStatus.ACTIVE, issue_status_id: ItemStatus.ACTIVE, issue_pre_created: MoreThan(0) },
        });
        if (!pressPublications.length) {
            console.log('No press publication found');
            this.loggerService.log('No press publication found', 'AutoCreateIssues');
            return;
        }
        for (const pressPublication of pressPublications) {
            const countIssue = await this.pressPublicationsService.repo.manager.count(IssueEntity, {
                where: { press_publication_id: pressPublication.id, publish_date: MoreThan(new Date()) },
            });
            if (countIssue < pressPublication.issue_pre_created) {
                const issueLasted = await this.pressPublicationsService.repo.manager.findOne(IssueEntity, {
                    where: { press_publication_id: pressPublication.id },
                    order: { publish_date: 'DESC' },
                });
                if (!issueLasted) {
                    console.log(`No issue found for press publication ${pressPublication.id}`);
                    this.loggerService.log(
                        `No issue found for press publication ${pressPublication.id}`,
                        'AutoCreateIssues'
                    );
                    continue;
                }
                let allCount = issueLasted.all_count;
                const issues = Array.from({ length: pressPublication.issue_pre_created - countIssue }, () => {
                    return this.pressPublicationsService.repo.manager.create(IssueEntity, {
                        name: `${pressPublication.issue_title} ${issueLasted.all_count}`,
                        status_id: ItemStatus.PENDING,
                        press_publication_id: pressPublication.id,
                        department_id: issueLasted.department_id,
                        all_count: allCount++,
                        //publish_date: '',//TODO:
                    });
                });
                return this.pressPublicationsService.repo.manager
                    .transaction(async (manager) => {
                        await manager.save(issues);
                    })
                    .then(() => {
                        console.log(`Created ${issues.length} issues for press publication ${pressPublication.id}`);
                        this.loggerService.log(
                            `Created ${issues.length} issues for press publication ${pressPublication.id}`,
                            'AutoCreateIssues'
                        );
                    })
                    .catch((error) => {
                        console.log(
                            `Error creating issues for press publication ${pressPublication.id}: ${error.message}`
                        );
                        this.loggerService.log(
                            `Error creating issues for press publication ${pressPublication.id}: ${error.message}`,
                            'AutoCreateIssues'
                        );
                    });
            }
        }
    }
}
