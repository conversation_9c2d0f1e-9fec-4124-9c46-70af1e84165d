import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LoggerService } from '../../loggers/logger.service';
import { MoreThan } from 'typeorm';
import { ItemStatus } from '../../commons/enums.common';
import { PressPublicationsService } from '../../modules/press-publications/services/press-publications.service';
import { IssueEntity } from '../../entities/issue.entity';
import { IssueType } from '../../entities/press-publication.entity';
import { addMonths, addWeeks, addYears } from 'date-fns';
import { getNextDayOfWeek } from '../../commons/helpers/common.helper';

@Injectable()
export class AutoCreateIssuesService {
    constructor(
        private readonly loggerService: LoggerService,
        private readonly pressPublicationsService: PressPublicationsService
    ) {}

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async handleCron() {
        const pressPublications = await this.pressPublicationsService.find({
            where: { status_id: ItemStatus.ACTIVE, issue_status_id: ItemStatus.ACTIVE, issue_pre_created: MoreThan(0) },
        });
        if (!pressPublications.length) {
            console.log('No press publication found');
            this.loggerService.log('No press publication found', 'AutoCreateIssues');
            return;
        }
        for (const pressPublication of pressPublications) {
            const countIssue = await this.pressPublicationsService.repo.manager.count(IssueEntity, {
                where: { press_publication_id: pressPublication.id, publish_date: MoreThan(new Date()) },
            });
            if (countIssue < pressPublication.issue_pre_created) {
                const issueLasted = await this.pressPublicationsService.repo.manager.findOne(IssueEntity, {
                    where: { press_publication_id: pressPublication.id },
                    order: { publish_date: 'DESC' },
                });
                let allCount = issueLasted ? issueLasted.all_count : pressPublication.issue_offset - 1;
                const startDate = issueLasted ? issueLasted.publish_date! : pressPublication.issue_start_date!;
                const publishDates: Date[] = [];

                if (pressPublication.issue_type_id === IssueType.YEARLY) {
                    for (let i = 1; i <= pressPublication.issue_pre_created - countIssue; i++) {
                        publishDates.push(addYears(startDate, i));
                    }
                } else if (pressPublication.issue_type_id === IssueType.MONTHLY) {
                    for (let i = 1; i <= pressPublication.issue_pre_created - countIssue; i++) {
                        publishDates.push(addMonths(startDate, i));
                    }
                } else if (pressPublication.issue_type_id === IssueType.WEEKLY) {
                    pressPublication.issue_days.forEach((day) => {
                        let nextDate = getNextDayOfWeek(startDate, day);
                        for (let i = 0; i < pressPublication.issue_pre_created - countIssue; i++) {
                            publishDates.push(addWeeks(nextDate, i));
                        }
                    });
                }

                const issues: IssueEntity[] = publishDates.map((publishDate) => {
                    allCount++;
                    return this.pressPublicationsService.repo.manager.create(IssueEntity, {
                        name: `${pressPublication.issue_title} ${allCount}`,
                        status_id: ItemStatus.PENDING,
                        press_publication_id: pressPublication.id,
                        department_id: pressPublication.department_id,
                        all_count: allCount,
                        publish_date: publishDate,
                    });
                });

                return this.pressPublicationsService.repo.manager
                    .transaction(async (manager) => {
                        await manager.save(issues);
                    })
                    .then(() => {
                        console.log(`Created ${issues.length} issues for press publication ${pressPublication.id}`);
                        this.loggerService.log(
                            `Created ${issues.length} issues for press publication ${pressPublication.id}`,
                            'AutoCreateIssues'
                        );
                    })
                    .catch((error) => {
                        console.log(
                            `Error creating issues for press publication ${pressPublication.id}: ${error.message}`
                        );
                        this.loggerService.log(
                            `Error creating issues for press publication ${pressPublication.id}: ${error.message}`,
                            'AutoCreateIssues'
                        );
                    });
            }
        }
    }
}
