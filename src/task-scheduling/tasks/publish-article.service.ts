import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LoggerService } from '../../loggers/logger.service';
import { DataSource } from 'typeorm';
import { ArticleEntity } from '../../entities/article.entity';
import { WorkflowEntity, WorkflowType } from '../../entities/workflow.entity';

@Injectable()
export class PublishArticleService {
    constructor(
        private readonly loggerService: LoggerService,
        private readonly dataSource: DataSource
    ) {}

    /**
     * Cron job to publish articles.
     * Run every minute.
     * <PERSON><PERSON><PERSON> tất cả bài viết có workflow.workflow_type_id = TIMING_PUBLISHING và publish_date <= now().
     * Update workflow_id = workflow_id của bài viết PUBLISHED theo từng department.
     */
    @Cron(CronExpression.EVERY_MINUTE)
    async handleCron() {
        try {
            // Get all articles that need to be published with their department_id
            const articlesToPublish = await this.dataSource
                .getRepository(ArticleEntity)
                .createQueryBuilder('article')
                .innerJoin('article.workflow', 'workflow')
                .select(['article.id', 'article.department_id', 'article.workflow_id'])
                .where('article.publish_date <= NOW()')
                .andWhere('workflow.workflow_type_id = :timingPublishingType', {
                    timingPublishingType: WorkflowType.TIMING_PUBLISHING
                })
                .getMany();

            if (articlesToPublish.length === 0) {
                console.log('No articles to publish');
                this.loggerService.log('No articles to publish', 'PublishArticle');
                return;
            }

            // Group articles by department_id
            const articlesByDepartment = articlesToPublish.reduce((acc, article) => {
                if (!acc[article.department_id]) {
                    acc[article.department_id] = [];
                }
                acc[article.department_id].push(article);
                return acc;
            }, {} as Record<number, ArticleEntity[]>);

            let totalPublished = 0;

            // Process each department separately
            for (const [departmentId, articles] of Object.entries(articlesByDepartment)) {
                const deptId = parseInt(departmentId);

                // Find PUBLISHED workflow for this department
                const publishedWorkflow = await this.dataSource.getRepository(WorkflowEntity).findOne({
                    where: {
                        workflow_type_id: WorkflowType.PUBLISHED,
                        department_id: deptId
                    },
                });

                if (!publishedWorkflow) {
                    console.log(`No published workflow found for department ${deptId}`);
                    this.loggerService.log(`No published workflow found for department ${deptId}`, 'PublishArticle');
                    continue;
                }

                // Find TIMING_PUBLISHING workflow for this department
                const timingPublishingWorkflow = await this.dataSource.getRepository(WorkflowEntity).findOne({
                    where: {
                        workflow_type_id: WorkflowType.TIMING_PUBLISHING,
                        department_id: deptId
                    },
                });

                if (!timingPublishingWorkflow) {
                    console.log(`No timing publishing workflow found for department ${deptId}`);
                    this.loggerService.log(`No timing publishing workflow found for department ${deptId}`, 'PublishArticle');
                    continue;
                }

                // Update articles for this department
                const articleIds = articles.map(article => article.id);
                const result = await this.dataSource
                    .getRepository(ArticleEntity)
                    .createQueryBuilder()
                    .update(ArticleEntity)
                    .set({ workflow_id: publishedWorkflow.id })
                    .where('id IN (:...articleIds)', { articleIds })
                    .andWhere('workflow_id = :timingWorkflowId', { timingWorkflowId: timingPublishingWorkflow.id })
                    .andWhere('department_id = :departmentId', { departmentId: deptId })
                    .execute();

                totalPublished += result.affected || 0;

                console.log(`Published ${result.affected} articles for department ${deptId}`);
                this.loggerService.log(`Published ${result.affected} articles for department ${deptId}`, 'PublishArticle');
            }

            if (totalPublished > 0) {
                console.log(`Total articles published: ${totalPublished}`);
                this.loggerService.log(`Total articles published: ${totalPublished}`, 'PublishArticle');
            }

        } catch (error) {
            console.log('Error publishing articles: ', error);
            this.loggerService.log('Error publishing articles: ' + error, 'PublishArticle');
        }
    }
}
